<?php


namespace app\index_v2\controller;


use api\jd_sdk\JdOrderN;
use api\wechat\Carer;
use app\admin_v2\command\early_warning\Material;
use app\admin_v2\command\early_warning\Price;
use app\admin_v2\service\TaxInvoiceService;
use app\common\model\bu\BuCardReceiveRecord;
use app\common\model\bu\BuOrder;
use app\common\model\bu\BuOrderCommodity;
use app\common\model\bu\BuQyDepartmentAll;
use app\common\model\db\DbAfterSaleOrders;
use app\common\model\db\DbCard;
use app\common\model\db\DbCommoditySet;
use app\common\model\db\DbCommoditySku;
use app\common\model\db\DbLog;
use app\common\model\db\DbLyPaySettle;
use app\common\model\db\DbLySynOrder;
use app\common\model\db\DbLyTransactionOrder;
use app\common\model\db\DbOrderInvoice;
use app\common\model\db\DbSeckill;
use app\common\model\db\DbSeckillCommodity;
use app\common\model\db\DbSupplierVerifyOrder;
use app\common\model\db\DbUserDraw;
use app\common\model\db\DbUserDrawRecord;
use app\common\model\db\InvoiceApplyDetailModel;
use app\common\net_service\ConsumeRule;
use app\common\net_service\LyPay;
use app\common\net_service\NetOrder;
use app\common\net_service\SendSms;
use app\common\service\BusiplatNevService;
use app\net_small\command\ExpireRefund;
use think\Cache;
use think\cache\driver\Redis;
use think\Controller;
use think\Hook;
use think\Queue;
use think\Request;
use tool\PhpExcel;

class Yang extends Controller
{
    public function __construct()
    {
        parent::__construct();
        $user = input('user_token');
        $sign = input('sign');
        if ($user != 'yang' || $sign != 'e2a6de83acf8b4c24f4b6f2396aac027') {
            die('未经授权，不允许访问');
        }
    }

    /**
     * 修改订单结算状态
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function updSettleStatus()
    {
        $order_code = input('order_code');
        if (empty($order_code)) {
            print_json(1, '订单编码不能为空');
        }
        $order_code_arr = explode(',', $order_code);
        $settle_status  = input('settle_status', '0');
        $upd            = [
            'settlement_state'  => $settle_status,
            'last_updated_date' => date('Y-m-d H:i:s'),
            'modifier'          => '手工修改结算状态'
        ];
        $re             = BuOrder::where(['order_code' => ['in', $order_code_arr]])->update($upd);
        if ($re) {
            $info = BuOrder::where(['order_code' => ['in', $order_code_arr]])->field('order_code,settlement_state')->select();
            print_json(0, '更新成功', $info);
        } else {
            print_json(1, '更新失败');
        }
    }


    /**
     * 修改结算记录表
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function updLySettle()
    {
        $id = input('id');
        if (empty($id)) {
            print_json(1, '结算记录id不能为空');
        }
        $settle_status = input('settle_status', 'S0'); // 结算中
        $map           = ['id' => $id];
        $data          = ['is_enable' => 1, 'settle_status' => $settle_status, 'modifier' => '手动修改'];
        $re            = DbLyPaySettle::where($map)->update($data);
        if ($re) {
            $info = DbLyPaySettle::where($map)->find();
            print_json(0, '更新成功', $info);
        } else {
            print_json(1, '更新失败');
        }
    }


    /**
     * 订单推送
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function pushOrder()
    {
        $order_code = input('order_code');
        if (empty($order_code)) {
            print_json(1, '订单编码不能为空');
        }
        $field     = 'id,order_code,order_status';
        $orderInfo = BuOrder::where('order_code', $order_code)->field($field)->find();
        if (empty($orderInfo)) {
            print_json(1, '订单不存在');
        }
        if (!in_array($orderInfo['order_status'], [1, 3, 8])) {
            Queue::push('app\common\queue\PushOrder', json_encode(['order_id' => $orderInfo['id']]), config('queue_type.push_order'));
            print_json(0, '已进入队列执行');
        } else {
            print_json(1, '订单状态:' . $orderInfo['order_status']);
        }
    }


    public function getQueueList()
    {
        ini_set('memory_limit', '3000M');
        $key   = input('redis_key');
        $start = input('start');
        $end   = input('end');
        $db = input('db',0);
        $redis = Cache::redisHandler();
        $nativeRedis = $redis->handler(); // 获取原生 Redis 客户端
        $nativeRedis->select($db); // 选择 Redis 数据库 1
        if (input('max')) {
            $max = 11;
        } else {
            $max = $nativeRedis->Llen($key);
        }
        if (input('list')) {
            $list = [];

        } else {
            $list = $nativeRedis->lrange($key, $start, $end);

        }
        $data = ['num' => $max, 'list' => $list];
        print_json(0, 'success', $data);
    }


    /**
     * 获取秒杀库存
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function getSeckillValue()
    {
        $seckill_id              = input('seckill_id');
        $redis                   = new Redis(config('cache'));
        $seckill_model           = new DbSeckill();
        $seckill_info            = $seckill_model->where('id', $seckill_id)->find();
        $seckill_commodity_model = new DbSeckillCommodity();
        $commodityIds            = $seckill_commodity_model->where('seckill_id', $seckill_info['id'])->column('commodity_id');
        $result                  = [];

        if ($seckill_info['seckill_type'] == 1) {
            foreach ($commodityIds as $item) {
                $redis_name = 'acKillCountById' . $seckill_id;
                $redis_name = $redis_name . $item;
                $num        = $redis->get($redis_name);
                $result[]   = [
                    $redis_name => $num
                ];
            }
        } else {
            // 循环场次
            $day = floor((strtotime($seckill_info['end_time']) - strtotime($seckill_info['start_time'])) / 86400);
            for ($i = 0; $i <= $day; $i++) {
                foreach ($commodityIds as $commodityId) {
                    $redis_name                  = 'acKillCountById' . $seckill_id;
                    $date                        = date('Y-m-d', strtotime("+" . $i . " day", strtotime($seckill_info['start_time'])));
                    $redis_name                  = $redis_name . $commodityId . ':' . $date;
                    $num                         = $redis->get($redis_name);
                    $result[$commodityId][$date] = [
                        $redis_name => $num
                    ];
                }
            }
        }

        print_json(0, 'success', $result);

    }


    /**
     * 获取启辰车主信息接口
     */
    public function getQcMemberCard()
    {
        $channel = input('channel');
        $oneId   = input('one_id');
        $unionId = input('unionid');
        $service = new Carer();
        $result  = $service->getMemberCard($channel, $oneId, $unionId);
        print_json(0, 'success', $result);
    }


    /**
     * 更新卡券状态
     */
    public function upd_card_status()
    {
        $id                = input('card_id');
        $validity_date_end = input('validity_date_end');
        $act_status        = input('act_status');
        $card_model        = new DbCard();
        $upd               = ['act_status' => $act_status, 'validity_date_end' => $validity_date_end];
        $re                = $card_model->where('id', $id)->update($upd);
        if ($re) {
            print_json(0, 'success');
        } else {
            print_json(1, 'error');
        }
    }


    /**
     * 更新订单商品状态
     */
    public function upd_order_commodity_status()
    {
        $order_code             = input('order_code');
        $order_commodity_status = input('order_commodity_status');
        $order_code_arr         = explode(',', $order_code);
        $model                  = new BuOrderCommodity();
        $upd                    = ['order_commodity_status' => $order_commodity_status];
        $re                     = $model->whereIn('order_code', $order_code_arr)->update($upd);
        if ($re) {
            print_json(0, 'success');
        } else {
            print_json(1, 'error');
        }
    }


    /**
     * 获取支付商户列表
     */
    public function query_merchant_info()
    {
        $lyPay = new LyPay();

        $brand_code = input('brand_code');
        $data       = [
            'brandCode' => $brand_code,
        ];
        $re         = $lyPay->queryMerchantInfo($data);
        if ($re->isSuccess()) {
            $list = $re->getData();
            print_json(0, 'success', $list);
        } else {
            print_json(1, $re->getMessage());
        }
    }


    public function getOrderSeckill()
    {
        $order_code      = input('order_code');
        $order_commodity = BuOrderCommodity::where('order_code', $order_code)->field('seckill_id,commodity_id,created_date')->find();
        if (empty($order_commodity['seckill_id'])) {
            print_json(1, 'error', '该订单没有参加秒杀');
        }
        // 查询秒杀类型
        $seckill_info = DbSeckill::where('id', $order_commodity['seckill_id'])->find();
        $screening    = '';
        if ($seckill_info['seckill_type'] == 2) {
            $screening = date('Y-m-d', strtotime($order_commodity['created_date']));
        }
        $redis_name = 'acKillCountById' . $order_commodity['seckill_id'] . $order_commodity['commodity_id']; //
        if ($screening != '') {
            $redis_name = $redis_name . ':' . $screening; // 场次
        }
        $redis_name_order = $redis_name . $order_code;
        $redis            = new Redis(config('cache'));
        $count            = $redis->get($redis_name);
        $count_order      = $redis->get($redis_name_order);
        print_json(0, 'success', ['count' => $count, 'count_order' => $count_order]);

    }


    // 测试消费返积分
    public function test_consume()
    {
        $order_code  = input('order_code');
        $order_model = new BuOrder();
        $upd         = [
            'order_status'     => 7,
            'settlement_time'  => date('Y-m-d H:i:s'),
            'settlement_state' => 1
        ];
        $order_model->where('order_code', $order_code)->update($upd);
        $service = new ConsumeRule();
        $service->addPoint($order_code);
    }


    public function test_seng_msg()
    {
        $type         = input('type', 1);
        $order_code   = input('order_code');
        $send_service = new SendSms();
        $orderId      = BuOrder::where('order_code', $order_code)->value('id');
        if ($type == 1) {
            $re = $send_service->shipments_msg($orderId);
        } elseif ($type == 2) {
            $re = $send_service->pickup_goods_msg($orderId);
        } else {
            $re = $send_service->return_money_msg($orderId);
        }
        dd($re);
    }


    /**
     * 修改同步联友数据
     */
    public function updLySynOrder()
    {
        $ids       = input('ids');
        $is_enable = input('is_enable', 1);
        $model     = new DbLySynOrder();
        $id_arr    = explode(',', $ids);
        $upd       = ['is_enable' => $is_enable];
        $re        = $model->whereIn('id', $id_arr)->update($upd);
        dd($re);
    }


    /**
     * 京东价格
     * @throws \PHPExcel_Exception
     * @throws \PHPExcel_Writer_Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function jd_price()
    {
        $is_send = input('is_send');
        $re      = Price::doIt();
        if ($is_send) {
            Price::sendMailer();
        }
        dd($re);
    }


    /**
     * 京东素材
     * @throws \PHPExcel_Exception
     * @throws \PHPExcel_Writer_Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function jd_material()
    {
        $is_send = input('is_send');
        $re      = Material::doIt();
        if ($is_send) {
            Material::sendMailer();
        }
        dd($re);
    }


    public function demo()
    {
        $files    = 'public/uploads/exports/异步订单统计_292.csv';
        $filename = '异步订单统计_292.csv';
        download($files, $filename);
        exit();
    }


    public function upd_draw_record_enable()
    {
        $id    = input('id');
        $model = new DbUserDrawRecord();
        $upd   = ['is_enable' => 0, 'modifier' => '手动修改'];
        $re    = $model->where('id', $id)->update($upd);
        dd($re);
    }


    public function upd_user_draw_enable()
    {
        $id    = input('id');
        $model = new DbUserDraw();
        $upd   = ['is_enable' => 0, 'modifier' => '手动修改'];
        $re    = $model->where('id', $id)->update($upd);
        dd($re);
    }


    // 获取订单信息
    public function getJdOrder()
    {
        $jdOrderId = input('jd_order_id');
        $jd_n      = new JdOrderN();
        $result    = $jd_n->selectJdOrder($jdOrderId);
        print_json(0, 'success', $result);
    }


    // 获取物流信息
    public function getJdOrderTrack()
    {
        $jdOrderId = input('jd_order_id');
        $jd_n      = new JdOrderN();
        $result    = $jd_n->orderTrack($jdOrderId);
        print_json(0, 'success', $result);
    }


    /**
     * 卡券售后
     */
    public function cardAfterSale()
    {
        $orderId     = input('order_id');
        $order_model = new BuOrder();
        $orderInfo   = $order_model->getOneByPk($orderId);
        $net_order   = new NetOrder();
        $re          = $net_order->cardAfterSale($orderInfo);
        print_json(0, 'success', $re);

    }


    /**
     * 卡券失效
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function invalidCoupon()
    {
        $coupon_codes         = input('coupon_code');
        $card_receive_record = new BuCardReceiveRecord();
        $coupon_codeArr = explode(',', $coupon_codes);
        $map  = ['coupon_code' => ['in', $coupon_codeArr]];
        $list = $card_receive_record->where($map)->select();
        $msg = [];
        if (!empty($list)) {
            foreach ($list as $key => $info) {
                if ($info['status'] == 3 || $info['quick_win_is_consume'] == 1) {
                    $msg[] = '核销码：'.$info['coupon_code'].'用户卡券已核销，不能失效';
                    continue;
                }
                $re = $card_receive_record->saveData(['status' => 4], ['coupon_code' => $info['coupon_code']]);
                if ($re) {

                    DbLog::create(
                        [
                            'type'         => 'invalid_coupon',
                            'send_note'    => json_encode_cn(['coupon_code' => $info['coupon_code']]),
                            'receive_note' => json_encode_cn($info),
                            'is_success'   => 'success'
                        ]
                    );

                    $arr1 = [
                        'event_type' => 3,
                        'user_id'    => $info['user_id'],
                        'card_id'    => $info['card_id'],
                        'coupon_code' => $info['coupon_code'],
                        'data'       => [
                            'coupon_code' => $info['coupon_code'],
                            'modifier'    => '手动修改',
                            'receive_id'  => '',
                        ],
                    ];
                    Hook::exec('app\\net_small\\behavior\\CardReceiveRecordBehavior', 'run', $arr1);
                    $msg[] = '核销码：'.$info['coupon_code'].'success';
                }

            }

        } else {
            foreach ($coupon_codeArr as $coupon_code) {
                $arr1 = [
                    'event_type' => 3,
                    'user_id'    => '',
                    'card_id'    => '',
                    'coupon_code' => $coupon_code,
                    'data'       => [
                        'coupon_code' => $coupon_code,
                        'modifier'    => '手动修改',
                        'receive_id'  => '',
                    ],
                ];
                Hook::exec('app\\net_small\\behavior\\CardReceiveRecordBehavior', 'run', $arr1);

                $msg[] = '核销码：'.$coupon_code.'success';
            }
        }
        print_json(0,'success', $msg);
    }


    /**
     * 修改卡券
     */
    public function updCard()
    {
        $card_id    = input('card_id');
        $is_succeed = input('is_succeed', 1);
        $model      = new DbCard();
        $data       = ['is_succeed' => $is_succeed];
        $re         = $model->where('id', $card_id)->update($data);
        print_json(0, 'success', ['re' => $re]);

    }

    public function updAfsNum()
    {
        $aft_id     = input('aft_id');
        $num        = input('num');
        $order_code = input('order_code');

        $after_order_model            = new DbAfterSaleOrders();
        $aft_info                     = $after_order_model->where('id', $aft_id)->find();
        $refund_goods                 = json_decode($aft_info['refund_goods'], true);
        $refund_goods_msg             = json_decode($aft_info['refund_goods_msg'], true);
        $refund_goods[0]['count']     = $num;
        $refund_goods_msg[0]['count'] = $num;

        $upd = [
            'refund_goods'      => json_encode_cn($refund_goods),
            'refund_goods_msg'  => json_encode_cn($refund_goods_msg),
            'last_updated_date' => date('Y-m-d H:i:s'),
        ];
        $s1  = $after_order_model->where('id', $aft_id)->update($upd);
        // 更新统计数据
        $ly_transaction_model = new DbLyTransactionOrder();
        $map                  = [
            'order_code'       => $order_code,
            'transaction_type' => 2,
            'is_enable'        => 1
        ];
        $s2                   = $ly_transaction_model->where($map)->update(['num' => $num]);

        // 更新供应商结算表
        $supplier_model = new DbSupplierVerifyOrder();
        $s3             = $supplier_model->where($map)->update(['num' => $num]);

        print_json(0, 'success', ['s1' => $s1, 's2' => $s2, 's3' => $s3]);

    }


    public function test_flat()
    {
        $now_time = date('Y-m-d H:i:s');
        $model    = new DbCommoditySet();
        $map      = [
            'is_enable'           => 0,
            'latest_listing_time' => ['elt', $now_time],
        ];
        $set      = $model->where($map)->select();
        foreach ($set as $val) {
            $val['commodity_set_id'] = $val['id'];
            unset($val['id']);
            Hook::listen('flat', $val);
        }
    }


    // 添加企业部门
    public function add_qy_department()
    {
        $input = input('post.');
        $data  = [
            'departid' => $input['wx_id'],
            'name'     => $input['name'],
            'parentid' => $input['wx_parent_id'],
            'dlr_code' => $input['code'],
            'creator'  => 'index_v2'
        ];

        $model = new BuQyDepartmentAll();
        $id = $model->insertGetId($data);
        print_json(0, 'ok', ['id'=>$id]);
    }


    // 到期订单自动退款
    public function dq_order_refund()
    {
        $data = input('get.');
        ExpireRefund::dq_order_refund($data['end_time'],$data['start_time'], $data['order_code'],1);
        print_json(1,'查看是否成功?');
    }


    /**
     * 修改商品sku的is_enable
     */
    public function updCommoditySkuEnable()
    {
        $ids = input('ids');
        $idArr = explode(',', $ids);
        $is_enable = input('is_enable', 0);
        $sku_model = new DbCommoditySku();
        $upd = [
            'is_enable' => $is_enable,
            'modifier' => 'upd_enable'
        ];
        $re = $sku_model->whereIn('id', $idArr)->update($upd);
        print_json(1,'查看是否成功?', $re);

    }


    /**
     * 结算自动开票
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function OrderSettleAfter()
    {
        $orderCodes = input('order_codes');
        $orderCodeArr = explode(',', $orderCodes);
        $order_model = new BuOrder();
        $order_list = $order_model->whereIn('order_code', $orderCodeArr)->select();
        foreach ($order_list as $orderInfo) {
            Queue::push('app\admin_v2\queue\OrderSettleAfter', json_encode($orderInfo), config('queue_type.order_settle_after'));
        }
        print_json(0,'查看是否成功?');
    }


    /**
     * 同步订单商品id
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function synOrderCommodityId()
    {
        $detail_model = new InvoiceApplyDetailModel();
        $order_commodity_model = new BuOrderCommodity();
        $list = $detail_model->where('is_enable',1)->select();
        foreach ($list as $key => $value) {
            $map = [
                'order_code' => $value['good_order_no'],
                'commodity_id' => $value['good_id'],
            ];
            $order_commodity_id = $order_commodity_model->where($map)->value('id');
            $map = ['id'=>$value['id']];
            $upd = [
                'order_commodity_id' => $order_commodity_id,
            ];
            $detail_model->where($map)->update($upd);
        }
        print_json(0,'查看是否成功?');

    }




    public function changeApplyDetail()
    {
        $input = input('get.');
        if (empty($input['id'])) {
            print_json(1,'缺少参数');
        }
        $id = $input['id'];
        unset($input['user_token']);
        unset($input['sign']);
        unset($input['id']);
        if (empty($input)) {
            print_json(1,'缺少参数');
        }
        $apply_detail_model = new InvoiceApplyDetailModel();
        $input['modifier'] = 'change_detail';
        $input['last_updated_date'] = date('Y-m-d H:i:s');
        $apply_detail_model->where('id', $id)->update($input);
        print_json(0,$apply_detail_model->getLastSql());
    }


    /**
     * 保存开票信息
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function saveOrderInvoice()
    {
        $order_codes = input('order_codes');
        $order_code_arr = explode(',', $order_codes);
        $tax_invoice_service = new TaxInvoiceService();
        $re = [];
        foreach ($order_code_arr as $order_code) {
            $re[$order_code] = $tax_invoice_service->saveOrderInvoice($order_code);
        }
        print_json(0,'success', $re);
    }


    /**
     * 充电桩通知联友
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function changeOrderInvoice()
    {
        $input = input('get.');
        if (empty($input['id'])) {
            print_json(1,'缺少参数');
        }
        $id = $input['id'];
        unset($input['user_token']);
        unset($input['sign']);
        unset($input['id']);
        if (empty($input)) {
            print_json(1,'缺少参数');
        }
        $order_invoice_model = new DbOrderInvoice();
        $input['modifier'] = 'change_invoice';
        $order_invoice_model->where('id', $id)->update($input);
        $sql = $order_invoice_model->getLastSql();
        print_json(0,'success', $sql);
    }


    /**
     * 充电桩通知联友
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */

    public function sendLyNev()
    {
        $type = input('type', 1);
        $order_code = input('order_code');
        $busiplatNevService = new BusiplatNevService();
        if ($type == 1) {
            // 下单
            $result = $busiplatNevService->create($order_code);
        } else {
            $result = $busiplatNevService->after($order_code);
        }
        print_json(0,'success', $result);
    }


}
