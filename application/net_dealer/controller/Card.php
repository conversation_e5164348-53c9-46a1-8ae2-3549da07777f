<?php

namespace app\net_dealer\controller;

use app\common\model\bu\BuCardReceiveRecord;
use app\common\model\bu\BuOrder;
use app\common\model\db\DbActivity;
use app\common\model\db\DbActivityCenterLog;
use app\common\model\db\DbCard;
use app\common\model\db\DbCardLog;
use app\common\model\db\DbCommodityCard;
use app\common\model\db\DbDlr;
use app\common\model\db\DbLog;
use app\common\model\db\DbUser;
use app\common\net_service\NetCardKafKa;
use app\common\net_service\NetUser;
use app\common\port\connectors\Member;
use app\common\validate\Card as CardValidate;
use app\common\validate\Dealer as DealerValidate;
use ForkModules\Traits\ResponseTrait;

use hg\apidoc\annotation as Apidoc;

use think\Hook;
use think\Model;
use think\Validate;

/**
 * 卡券相关
 * @Apidoc\Group("dealer")
 */
class Card extends Common
{
    use ResponseTrait;

    private $shop_cart_model;

    public function __construct()
    {
        parent::__construct();
    }


    /**
     * @Apidoc\Title("速赢卡券核销")
     * @Apidoc\Author("llj")
     * @Apidoc\Url("/net-dealer/card/consume-coupon")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("核销 速赢")
     *
     * @Apidoc\Param("coupon_code",type="string", require=true,desc="卡券核销码")
     * @Apidoc\Param("dlr_code",type="string", require=true,desc="经销商编码")
     *
     * @Apidoc\Returned("msg",type="string", desc="说明")
     * @Apidoc\Returned("result",type="string", desc="状态：1为成功")
     * @Apidoc\Returned("rows",type="string", desc="返参（卡券核销码）")
     *
     */
    public function consumeCoupon(CardValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("consume_coupon")->check($requestData);
        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }

        $data = [
            'msg'    => '核销失败',
            'result' => '40104010403',
            'rows'   => $requestData['coupon_code'],
        ];

        $card_receive_record = new BuCardReceiveRecord();
        $info                = $card_receive_record->where(['coupon_code' => $requestData['coupon_code']])->find();
        DbLog::create(
            [
                'type'         => 'consume_coupon',
                'send_note'    => json_encode($requestData),
                'receive_note' => json_encode($info),
                'is_success'   => 'success'
            ]
        );
        if (!$info) {
            $data['msg'] = '卡券领取记录不存在';

        } else {
            if ($info['status'] == 4) {
                $data['msg'] = '用户卡券已失效，不能核销';
            } else {
                $re = $card_receive_record->where(['coupon_code' => $requestData['coupon_code']])->update(['status' => 3, 'consume_dlr_code' => $requestData['dlr_code'] ?? "", 'consume_date' => date('Y-m-d H:i:s')]);
              
                if ($re) {
                    $data = [
                        'msg'    => '核销成功',
                        'result' => '1',
                        'rows'   => $requestData['coupon_code'],
                    ];
                    return $this->setResponseData($data)->send();
                }
            }
        }
        return $this->setResponseError($data, 403)->send();
    }

    /**
     * @Apidoc\Title("速赢卡券同步商城领取")
     * @Apidoc\Author("llj")
     * @Apidoc\Url("/net-dealer/card/get-coupon")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("领取 速赢 1020")
     *
     * @Apidoc\Param("coupon_code",type="string", require=true,desc="卡券核销码")
     * @Apidoc\Param("card_id",type="int", require=true,desc="卡券中心id")
     * @Apidoc\Param("phone",type="string", require=true,desc="用户手机号")
     * @Apidoc\Param("member_id",type="string", require=false,desc="用户member_id")
     * @Apidoc\Param("get_dlr_code",type="string", require=false,desc="获取卡券专营店编码")
     * @Apidoc\Param("intention_dlr_id",type="int", require=false,desc="意向门店id")
     * @Apidoc\Param("intention_dlr_id",type="int", require=false,desc="意向门店id")
     * @Apidoc\Param("validity_date_start",type="datetime", require=false,desc="有效期开始时间：格式（2022-11-11 11:11:11）")
     * @Apidoc\Param("validity_date_end",type="datetime", require=false,desc="有效期结束时间：格式（2022-11-11 11:11:11）")
     *
     * @Apidoc\Returned("msg",type="string", desc="说明")
     * @Apidoc\Returned("result",type="string", desc="状态：1为成功")
     * @Apidoc\Returned("rows",type="string", desc="返参（卡券核销码）")
     *
     */
    public function getCoupon(CardValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("get_coupon")->check($requestData);
        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
       // $requestData = '{"coupon_code":"2001054100","phone":"13826289847","card_id":307160,"validity_date_start":"2024-12-09 00:00:00","validity_date_end":"2024-12-31 23:59:59"}';
       // $requestData = json_decode($requestData,true);
        $member_id           = $requestData['member_id'] ?? '';
        $phone               = $requestData['phone'];
        $card_id             = $requestData['card_id'];
        $coupon_code         = $requestData['coupon_code'];
        $get_dlr_code        = $requestData['get_dlr_code'] ?? '';
        $intention_dlr_id    = $requestData['intention_dlr_id'] ?? 0;
        $validity_date_start = $requestData['validity_date_start'] ?? 0;
        $validity_date_end   = $requestData['validity_date_end'] ?? 0;

        $log_id = (new DbCardLog())->insertGetId(
            [
                'event_type'        => 'getCoupon',
                'request_info'      => json_encode($requestData),
                'card_id'           => $card_id,
                'created_date'      => $validity_date_start,
                'last_updated_date' => $validity_date_end,
            ]
        );

        $re_data = [
            'msg'    => '发券失败',
            'result' => '40104010403',
            'rows'   => $coupon_code,
        ];
        $is_error = 0;
        if (empty($phone) || empty($card_id) || empty($coupon_code)) {
            $re_data['msg'] = '参数错误';
            $is_error       = 1;
        }
        $card_receive = (new BuCardReceiveRecord())->getOne(['where' => ['coupon_code' => $coupon_code]]);
        if (!empty($card_receive) || !getRedisLock('coupon_code_' . $coupon_code)) {
            $re_data['msg'] = '核销码已存在';
            $is_error       = 1;
        }

        if ($is_error == 0) {
            $user_model = new DbUser();
            if (!empty($member_id)) {
                $mem_where = ['plat_id' => $member_id];
            } else {
                $mem_where = ['mid_phone' => $phone, 'plat_id' => ['neq','******']];
            }
            $field = "openid,unionid bind_unionid,dlr_code as channel_type,plat_id,plat_id member_id,id,name,phone,unionid,one_id";
            $user  = $user_model->getOne(['where' => $mem_where, 'field' => $field, 'order' => 'id asc']);
            if (empty($user)) {
                $member = Member::create('member')->search(['phone' => $phone]);
                if (empty($member['member_id'])) {
                    $re_data['msg'] = '用户不存在';
                    (new DbCardLog())->where(['id' => $log_id])->update(['response_info' => json_encode($re_data)]);
                    return $this->setResponseError($re_data, 403)->send();
                }

                $data = [
                    'headimg_market'    => $member['avatar'],
                    'headimg_app'       => $member['app_avatar'],
                    'nickname_market'   => $member['nickname'],
                    'nickname_app'      => $member['app_nickname'],
                    'one_id'            => $member['oneid'],
                    'mid_phone'         => $member['phone'],
                    'modifier'          => 'get_coupon_new',
                    'plat_id'           => $member['member_id'],
                    'last_updated_date' => date('Y-m-d H:i:s'),
                ];
                $user_model->insert($data);
                $user = $user_model->getOne(['where' => ['plat_id' => $member['member_id']], 'field' => $field]);
            }
            $card         = (new DbCard())->getOne(['where' => ['quick_win_card_id' => $card_id]]);

            $channel_type = 'COUPON';
//        if ($card['shelves_type'] == 6) {
//            $channel_type = 'PZ1ASM';
//        } elseif ($card['shelves_type'] == 7) {
//            $channel_type = 'QCSM';
//        }

//            $net_user       = new NetUser();
//            $user['brand']  = 1;
//            $get_card       = $net_user->get_card($card['id'], 'get_card_by_oupon', 23, $user, $channel_type, 0, '', $get_dlr_code, '', ['coupon_code' => $coupon_code, 'intention_dlr_id' => $intention_dlr_id, 'validity_date_start' => $validity_date_start, 'validity_date_end' => $validity_date_end]);
//            $re_data['msg'] = $get_card['msg'];
//            if ($get_card['code'] == 200) {
//                $re_data['result'] = 1;
//            }
            $source = 23;
            $act_code = "get_card_by_oupon";
            $poster_id = 0;
            $creator = '';
            $hit_type_code = $get_dlr_code;
            $order_model  = new BuOrder();
            $cardModel = new DbCard();
            $order                   = $order_model->getOne(['where' => ['id' => $act_code]]);
            $channel                 = $order['channel'] ?? '';
            $pick_delivery_card_code = $order['pick_delivery_card_code'] ?? '';
            $pick_up_order_code      = $order['pick_up_order_code'] ?? '';
            $status = BuCardReceiveRecord::STATUS_KEY1;
            $buCardReceiveRecord = new BuCardReceiveRecord();
            if ($card['card_type'] == 7) {
                $vin          = isset($order['order_vin']) ? $order['order_vin'] : '';
                $get_dlr_code = '';
                if (!empty($pick_up_order_code) && in_array($channel, ['3', '5', '7'])) {
                    $status = BuCardReceiveRecord::STATUS_KEY5;
                }
            } else {
                $vin = isset($car['vin']) ? $car['vin'] : '';
            }
            $cardReceiveRecordInfo = $buCardReceiveRecord->where(['coupon_code'=>$coupon_code,'is_enable'=>1])->find();
            $activity_id = $card['activity_id'];
            if (isset($validity_date_start) && $validity_date_end) {
                $date_start = $validity_date_start;
                $date_end   = $validity_date_end;
            } else {
                if ($card['date_type'] == 1) {
                    $date_start = $validity_date_start;
                    $date_end   = $validity_date_end . ' 23:59:59';
                } else {
                    //固定时长专用，领取后多少天内有效，单位为天(有效天数)
                    //  `fixed_begin_term` '固定时长专用，表示自领取后多少天开始生效，当天生效填写0，单位为天',
                    $date_start = date('Y-m-d H:i:s', strtotime(sprintf(" +%s day", $card['fixed_begin_term'])));
                    $date_end   = date('Y-m-d H:i:s', strtotime(sprintf(" +%s day", $card['fixed_term'])));
                }
            }
            if(empty($cardReceiveRecordInfo)){
                $indata               = array(
                    'user_id'                 => $user['id'],
                    'vin'                     => $vin,
                    'license_plate'           => isset($car['car_no']) ? $car['car_no'] : '',
                    'name'                    => isset($car['name']) ? $car['name'] : '',
                    'source'                  => $source,
                    'act_id'                  => $act_code,
                    'phone'                   => isset($car['mobile']) ? $car['mobile'] : '',
                    'card_id'                 => $card['id'],
                    'dlr_code'                => $channel_type,
                    'openid'                  => $user['unionid'],
                    'poster_id'               => $poster_id,
                    'creator'                 => $creator,
                    'get_dlr_code'            => $get_dlr_code,
                    'car_18n'                 => isset($user['car_18n']) ? $user['car_18n'] : '',
                    'created_date'            => date('Y-m-d H:i:s', time() + mt_rand(1, 10)),
                    'coupon_code'             => $coupon_code,
                    'intention_dlr_id'        => $intention_dlr_id,
                    'validity_date_start'     => $date_start,
                    'validity_date_end'       => $date_end,
                    'consume_order_code'      => $pick_up_order_code,
                    'coupon_center_flag'      => $requestData['coupon_center_flag'] ?? '',
                    'intention_brand_id'      => $requestData['intention_brand_id'] ?? '',
                    'intention_car_series_id' => $requestData['intention_car_series_id'] ?? '',
                    'intention_car_type_id'   => $requestData['intention_car_type_id'] ?? '',
                    'intention_store'         => $requestData['intention_store'] ?? '',
                    'reserve_date'            => $requestData['reserve_date'] ?? '',
                    'reserve_id'              => $requestData['reserve_id'] ?? '',
                    'activity_id'             => $activity_id,
                );

//                $dbActivityObj      = new DbActivity();
//                if($card['activity_id']>0){
//                    $dbActivityCardInfo = $dbActivityObj->where(['activity_id' => $card['activity_id'], 'is_enable' => 1])->find();
//                    if ($dbActivityCardInfo['select_obj'] == 2 || $dbActivityCardInfo['select_obj'] == 3) {
//                        $indata['receive_vin'] = $user['vin'];
//                    }
//                }
                $card_code             = $channel_type  . date('YmdHis').$coupon_code;
                $indata['is_get_card']   = 1;
                $indata['card_code']     = $card_code;
                $indata['status']        = $status;
                $indata['hit_type_code'] = $hit_type_code;
                $buCardReceiveRecord->insertGetId($indata);
                $where = ['id' => $card['id'], 'available_count' => ['>', 0], 'is_enable' => 1];
                $res = $cardModel->where($where)->setDec('available_count', 1);
            }else{
                $updata = ['intention_dlr_id'=>$intention_dlr_id,'validity_date_start' => $date_start, 'validity_date_end' => $date_end,'activity_id'=>$activity_id];
                $buCardReceiveRecord->where(['coupon_code'=>$coupon_code,'is_enable'=>1])->update($updata);
            }
            $re_data['msg'] = "领取成功";
        }
        (new DbCardLog())->where(['id' => $log_id])->update(['user_id' => $user['id'] ?? 0, 'response_info' => json_encode_cn($re_data)]);
        if ($re_data['result'] == 1){
            return $this->setResponseData($re_data)->send();
        }
        return $this->setResponseError($re_data, $get_card['code'] ?? 403)->send();
    }

    protected function _getOrderNo($preStr = '', $length = 7)
    {
        $chars = "0123456789";
        $str = "";
        for ($i = 0; $i < $length; $i++) {
            $str .= substr($chars, mt_rand(0, strlen($chars) - 1), 1);
        }

        return $preStr . $str;
    }

    /**
     * @Apidoc\Title("专营店")
     * @Apidoc\Author("yang")
     * @Apidoc\Url("/net-dealer/dealer/list")
     * @Apidoc\Method("GET")
     * @Apidoc\Tag("专营店列表")
     *
     * @Apidoc\Param("brand",type="int", require=true,desc="品牌 1:NISSAN 2:启辰 3:英菲尼迪 4:雷诺 5:起亚")
     * @Apidoc\Param("update_date",type="string", require=false,desc="更新时间")
     *
     * @Apidoc\Returned("msg",type="string", desc="说明")
     * @Apidoc\Returned("result",type="string", desc="状态：1为成功")
     * @Apidoc\Returned("rows",type="array/json", desc="返参（卡券核销码）")
     * @Apidoc\Returned("key", type="array/json", desc="下标",
     *      @Apidoc\Returned ("id",type="int",desc="主键id"),
     *      @Apidoc\Returned ("dlr_name",type="string",desc="专营店名称"),
     *      @Apidoc\Returned ("dlr_code",type="string",desc="专营店编码"),
     *      @Apidoc\Returned ("base_dlr_id",type="string",desc="专营店基础支付id"),
     *      @Apidoc\Returned ("ly_bank_name",type="string",desc="签约银行"),
     *      @Apidoc\Returned ("ly_bank_code",type="string",desc="银行编码 01:招行 02:工行"),
     *     ),
     * ),
     */
    public function getDlrList()
    {
        $input    = input('get.');
        $rules    = [
            'brand' => ['require'],
        ];
        $validate = new Validate($rules);
        if (!$validate->check($input)) {
            return $this->setResponseError($validate->getError(), 403)->send();
        }

        $map = ['brand_type' => $input['brand'], 'is_enable' => 1, 'base_dlr_id' => ['neq', '']];
        if (!empty($input['update_date'])) {
            $map['last_updated_date'] = ['>=', $input['update_date']];
        }
        $field = 'id,dlr_name,dlr_code,base_dlr_id,ly_bank_name,ly_bank_code';
        $list  = DbDlr::where($map)->field($field)->select();
        $data  = [
            'msg'    => 'success',
            'result' => '1',
            'rows'   => $list,
        ];
        return $this->setResponseData($data)->send();
    }

    /**
     * @Apidoc\Title("速赢卡券失效")
     * @Apidoc\Author("llj")
     * @Apidoc\Url("/net-dealer/card/consume-coupon")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("核销 速赢")
     *
     * @Apidoc\Param("coupon_code",type="string", require=true,desc="卡券核销码")
     *
     * @Apidoc\Returned("msg",type="string", desc="说明")
     * @Apidoc\Returned("result",type="string", desc="状态：1为成功")
     * @Apidoc\Returned("rows",type="string", desc="返参（卡券核销码）")
     *
     */
    public function invalidCoupon(CardValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $dbActivityCenterLog =  new DbActivityCenterLog();
        $logData=[
            'activity_id'=>"",
            'request_id'=>'consumeCoupon',
            'oneid'=>"",
            'request_url'=>"",
            'request_info'=>json_encode($requestData),
            'response_info'=>""
        ];
        $logid = $dbActivityCenterLog->insertGetId($logData);
        $result      = $validate->scene("invalid_coupon")->check($requestData);
        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }

        $data = [
            'msg'    => '失效失败',
            'result' => '40104010403',
            'rows'   => $requestData['coupon_code'],
        ];

        $card_receive_record = new BuCardReceiveRecord();
        $info                = $card_receive_record->where(['coupon_code' => $requestData['coupon_code']])->find();
        DbLog::create(
            [
                'type'         => 'invalid_coupon',
                'send_note'    => json_encode($requestData),
                'receive_note' => json_encode($info),
                'is_success'   => 'success'
            ]
        );
        if (!$info) {
            $data['msg'] = '卡券领取记录不存在';
        } else {
            if ($info['status'] == 3 || $info['quick_win_is_consume'] == 1) {
                $data['msg'] = '用户卡券已核销，不能失效';
            } else {
                $re = $card_receive_record->saveData(['status' => 4], ['coupon_code' => $requestData['coupon_code']]);
                if ($re) {
                    $arr1 = [
                        'id' => $info['id'],
                        'event_type' => 3,
                        'user_id'    => $info['user_id'],
                        'card_id'    => $info['card_id'],
                        'data'       => [
                            'coupon_code' => $requestData['coupon_code'],
                            'modifier'    => '系统操作',
                            'receive_id'  => '',
                        ],
                    ];
                    Hook::exec('app\\net_small\\behavior\\CardReceiveRecordBehavior', 'run', $arr1);

                    $data = [
                        'msg'    => '失效成功',
                        'result' => '1',
                        'rows'   => $requestData['coupon_code'],
                    ];
                    return $this->setResponseData($data)->send();
                }
            }
        }
        return $this->setResponseError($data, 403)->send();
    }

    /**
     * @Apidoc\Title("速赢卡券获取商城相关卡券信息")
     * @Apidoc\Author("llj")
     * @Apidoc\Url("/net-dealer/card/coupon-msg")
     * @Apidoc\Method("GET")
     * @Apidoc\Tag("卡券 速赢")
     *
     * @Apidoc\Param("coupon_codes",type="string", require=false,desc="卡券核销码，多个用,隔开（跟卡券中心id二选一）")
     * @Apidoc\Param("coupon_ids",type="string", require=false,desc="卡券中心id，多个用,隔开（跟卡券核销码二选一）")
     *
     * @Apidoc\Returned("msg",type="string", desc="说明")
     * @Apidoc\Returned("result",type="string", desc="状态：1为成功")
     * @Apidoc\Returned("rows",type="string", desc="返参（卡券核销码）",
     *      @Apidoc\Returned("card_id",type="int", desc="商城卡券id"),
     *      @Apidoc\Returned("coupon_code",type="string", desc="卡券核销码"),
     *      @Apidoc\Returned("act_code",type="string", desc="商城订单id"),
     *      @Apidoc\Returned("status",type="int", desc="用户卡券状态：1领取，2未领取,3核销,4已失效"),
     *      @Apidoc\Returned("card_type",type="int", desc="卡劵类型：1立减券，2折扣劵，6到店代金券，7取送车券"),
     *      @Apidoc\Returned("coupon_id",type="int", desc="卡劵中心id"),
     *      @Apidoc\Returned("goods_data",type="array/json", desc="适用商品信息",
     *          @Apidoc\Returned("id",type="int", desc="商品id"),
     *          @Apidoc\Returned("commodity_name",type="string", desc="商品名称"),
     *      ),
     * )
     *
     */
    public function getCouponMsg(CardValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("coupon-msg")->check($requestData);
        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        $coupon_codes = $requestData['coupon_codes'] ?? '';
        $coupon_ids   = $requestData['coupon_ids'] ?? '';
        if (empty($coupon_codes) && empty($coupon_ids)) return $this->setResponseError('核销吗和卡券id必须传一个')->send();

        $BuCardReceiveRecord = new BuCardReceiveRecord();
        $DbCard              = new DbCard();
        $DbCommodityCard     = new DbCommodityCard();
        if (!empty($coupon_codes)) {
            $coupon_codes = array_filter(explode(',', $coupon_codes));
            $field = 'card_id,coupon_code,act_id act_code,status';
            $coupon_list  = $BuCardReceiveRecord->field($field)->where(['coupon_code' => ['in', $coupon_codes]])->select();
            $not_coupon = $coupon_codes;
            if($coupon_list){
                $have_coupon =  array_column($coupon_list,'coupon_code');
                $not_coupon = array_diff($coupon_codes,$have_coupon);
            }
            if($not_coupon){
                //如果不存在的时候就请求卡券接口获取领券记录
                $card_kafka_service = new NetCardKafKa();
                $res = $card_kafka_service->getCouponReceiveRecord(implode(',',$not_coupon),'','',0,'kab_kafka');
//                foreach ($not_coupon as $c_v){
//                }
                if ($res->isSuccess()) {
                    $coupon_list  = $BuCardReceiveRecord->field($field)->where(['coupon_code' => ['in', $coupon_codes]])->select();
                }
                //再查一次获取最新的卡券信息
            }

            if (!empty($coupon_list)) {
                $card_ids  = array_column($coupon_list, 'card_id');
                $card_list = $DbCard->field('id card_id,card_type,quick_win_card_id')->where(['id' => ['in', $card_ids]])->select();
                foreach ($coupon_list as &$v) {
                    foreach ($card_list as $vl) {
                        if ($v['card_id'] == $vl['card_id']) {
                            $v['card_type'] = $vl['card_type'];
                            $v['coupon_id'] = $vl['quick_win_card_id'];
                        }
                    }
                }
            }
        } else {
            $coupon_list = $DbCard->field('id card_id,card_type,quick_win_card_id')->where(['quick_win_card_id' => ['in', $coupon_ids]])->select();
            if (!empty($coupon_list)) {
                $card_ids = array_column($coupon_list, 'card_id');
            }
        }

        if (!empty($card_ids)) {
            $goods_list  = $DbCommodityCard->alias('a')->field('b.commodity_id,b.commodity_name,a.card_id')->join('t_db_commodity_flat b', 'a.commodity_set_id=b.commodity_set_id and find_in_set(a.card_id, b.card_id)')->where(['a.card_id' => ['in', $card_ids], ['exp', " find_in_set(1,b.sales_channel)"]])->select();
            $coupon_list = collection($coupon_list)->toArray();
            foreach ($coupon_list as &$v) {
                $v['goods_data'] = [];
                foreach ($goods_list as $vl) {
                    if ($v['card_id'] == $vl['card_id']) {
                        $v['goods_data'][] = $vl;
                    }
                }
            }
        }

        $data = [
            'msg'    => '获取成功',
            'result' => '1',
            'rows'   => $coupon_list,
        ];
        return $this->setResponseData($data)->send();

    }


    /**
     * 获取用户人群包卡券
     * @Apidoc\Title("获取人群包卡券")
     * @Apidoc\Author("yang")
     * @Apidoc\Url("/net-dealer/card/get-user-cards")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("卡券 速赢")
     *
     * @Apidoc\Param("member_id",type="string", require=true,desc="member_id")
     * @Apidoc\Param("card_id",type="array", require=true,desc="卡券中心id")
     * @Apidoc\Returned("card_id", type="array", desc="卡券id" )
     */
    public function getUserCards(DealerValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("get_user_cards")->check($requestData);

        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        $mid = nissanDecrypt($requestData['member_id']);
        if (!$mid) {
            return $this->setResponseError('用户信息缺失', 404)->send();
        }
        $user_model = new DbUser();
        $where      = ['plat_id' => $mid, 'is_enable' => 1];
        $user       = $user_model->getOne(['where' => $where]);
        if (empty($user)) {
            return $this->setResponseError('用户不存在', 404)->send();
        }
        $user['bind_unionid'] = $user['unionid'];
        $card_model = new DbCard();

        $map = [
            'quick_win_card_id' => ['in', $requestData['card_id']],
            'is_enable'         => 1
        ];

        $cardIds = $card_model->where($map)->column('id');
        $service = new \app\common\net_service\Common();
        $cardIds = $service->canGetCards($user, $cardIds, "GWSM");
        $quick_win_card_ids = [];
        if (!empty($cardIds)) {
            $map                = [
                'id'        => ['in', $cardIds],
                'is_enable' => 1
            ];
            $quick_win_card_ids = $card_model->where($map)->column('quick_win_card_id');
        }
        return $this->setResponseData(['card_id' => $quick_win_card_ids])->send();
    }
}
