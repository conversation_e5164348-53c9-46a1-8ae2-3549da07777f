<?php


namespace app\admin_v2\queue;


use app\common\model\db\DbExports;
use app\common\model\new_media\NewMediaCommodityCard;
use app\common\model\new_media\NewMediaCommoditySku;
use app\common\model\new_media\NewMediaCommoditySpareParts;
use app\common\model\new_media\NewMediaCommodityWorkingHours;
use think\Exception;
use think\queue\Job;
use tool\Logger;
use tool\PhpExcelPage;

class NewMediaCommodityQueue extends Base
{
    /**
     *
     * @param Job $job
     * @param $data
     */
    public function fire(Job $job, $data)
    {
        ini_set('memory_limit', '1024M');
        $info         = json_decode($data, true);
        $map          = $info['map'];
        $exportId     = $info['id'];
        $export_model = new DbExports();
        $export_model->where('id', $exportId)->update(['export_status' => 1]);

        try {
            $pageExcel = new PhpExcelPage();
            $pageExcel->setSheetTitle('抖音商品拉取表');
            $title = [
                'A'  => '新媒体', 'B' => '新媒体商品名称', 'C' => '新媒体商品ID', 'D' => '新媒体SKU名称', 'E' => '新媒体SKUID',
                'F'  => '新媒体商品类型', 'G' => '新媒体商品分类', 'H' => '次数', 'I' => '备件类别', 'J' => '备件编码',
                'K'  => '备件名称', 'L' => '备件原价', 'M' => '备件活动价', 'N' => '备件数量', 'O' => '工时类别', 'P' => '工时编码',
                'Q'  => '工时名称', 'R' => '工时原价', 'S' => '工时活动价', 'T' => '工时数量', 'U' => '新媒体备件活动', 'V' => '新媒体工时活动',
                'W'  => '厂家活动 - 补贴比例', 'X' => '新媒体优惠券 - 补贴比例', 'Y' => '新媒体活动 - 补贴比例', 'Z' => '新媒体会员优惠 - 补贴比例',
                'AA' => '新媒体积分优惠 - 补贴比例', 'AB' => '新媒体通兑券 - 补贴比例', 'AC' => '次卡1原价', 'AD' => '次卡2原价',
                'AE' => '次卡3原价', 'AF' => '次卡4原价', 'AG' => '次卡5原价', 'AH' => '次卡6原价', 'AI' => '次卡7原价',
                'AJ' => '次卡8原价', 'AK' => '次卡9原价', 'AL' => '次卡10原价'
            ];
            $pageExcel->setTitle($title);
            $widthArr = [
                'A'  => '30', 'B' => '30', 'C' => '30', 'D' => '30', 'E' => '50', 'F' => '30',
                'G'  => '30', 'H' => '30', 'I' => '30', 'J' => '50', 'K' => '30', 'L' => '30',
                'M'  => '30', 'N' => '30', 'O' => '30', 'P' => '30', 'Q' => '30', 'R' => '30',
                'S'  => '30', 'T' => '30', 'U' => '30', 'V' => '30', 'W' => '30', 'X' => '30',
                'Y'  => '30', 'Z' => '30', 'AA' => '30', 'AB' => '30', 'AC' => '30', 'AD' => '30',
                'AE' => '50', 'AF' => '30', 'AG' => '30', 'AH' => '30', 'AI' => '30', 'AJ' => '50',
                'AK' => '30', 'AL' => '30',

            ];
            $pageExcel->setWidth($widthArr);
            $totalPage          = 10;
            $limit              = 1000;
            $commodity_type_arr = \app\common\model\new_media\NewMediaCommodity::$commodity_type_arr;

            for ($i = 0; $i < $totalPage; $i++) {
                $list = $this->getData($map, $i, $limit);
                if (empty($list)) {
                    break;
                }
                $data = [];
                foreach ($list as $datum) {
                    $data[] = [
                        'A'  => $datum['platform'],
                        'B'  => $datum['commodity_name'],
                        'C'  => $datum['commodity_id'],
                        'D'  => $datum['sku_name'],
                        'E'  => $datum['sku_id'],
                        'F'  => $commodity_type_arr[$datum['commodity_type']] ?? '',
                        'G'  => $datum['commodity_class'],
                        'H'  => $datum['type_value'],
                        'I'  => $datum['spare_parts_type'],
                        'J'  => $datum['spare_part']['spare_parts_code'],
                        'K'  => $datum['spare_part']['spare_parts_name'],
                        'L'  => $datum['spare_part']['spare_parts_price'],
                        'M'  => $datum['spare_part']['spare_parts_activity_price'],
                        'N'  => $datum['spare_part']['spare_parts_num'],
                        'O'  => $datum['working_hours_type'],
                        'P'  => $datum['working_hours']['working_hours_code'],
                        'Q'  => $datum['working_hours']['working_hours_name'],
                        'R'  => $datum['working_hours']['working_hours_price'],
                        'S'  => $datum['working_hours']['working_hours_activity_price'],
                        'T'  => $datum['working_hours']['working_hours_num'],
                        'U'  => $datum['spare_parts_activity'],
                        'V'  => $datum['working_hours_activity'],
                        'W'  => $datum['factory_activity_ratio'],
                        'X'  => $datum['card_ratio'],
                        'Y'  => $datum['activity_ratio'],
                        'Z'  => $datum['member_ratio'],
                        'AA' => $datum['integral_ratio'],
                        'AB' => $datum['voucher_ratio'],
                        'AC' => $datum['card'][0] ?? '',
                        'AD' => $datum['card'][1] ?? '',
                        'AE' => $datum['card'][2] ?? '',
                        'AF' => $datum['card'][3] ?? '',
                        'AG' => $datum['card'][4] ?? '',
                        'AH' => $datum['card'][5] ?? '',
                        'AI' => $datum['card'][6] ?? '',
                        'AJ' => $datum['card'][7] ?? '',
                        'AK' => $datum['card'][8] ?? '',
                        'AL' => $datum['card'][9] ?? '',
                    ];
                }

                $pageExcel->setData($data, $i + 1, $limit);

            }
            $pageExcel->saveFile('新媒体商品维护表_' . $exportId.'.xlsx');
            $path = $pageExcel->getFilePath();
            $upd  = [
                'export_status' => 2,
                'file_address'  => $path
            ];
            $export_model->where('id', $exportId)->update($upd);
        } catch (Exception $e) {
            $msg = $e->getMessage();
            Logger::error('NewMediaCommodityQueue: ' . $msg);

            $upd = [
                'export_status' => 3,
                'modifier'  => $msg
            ];
            $export_model->where('id', $exportId)->update($upd);

        }

        $job->delete();

    }


    /**
     * 获取数据
     * @param $map
     * @param $page
     * @return \think\Paginator
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    private function getData($map, $page, $limit)
    {
        $sku_model           = new NewMediaCommoditySku();
        $spare_part_model    = new NewMediaCommoditySpareParts();
        $working_hours_model = new NewMediaCommodityWorkingHours();
        $card_model          = new NewMediaCommodityCard();

        $list = $sku_model->alias('a')
            ->join('t_new_media_commodity b', 'a.commodity_id=b.commodity_id')
            ->where($map)
            ->limit($page*$limit,$limit)
            ->select();
        foreach ($list as $item) {
            $map                = [
                'sku_id'    => $item['sku_id'],
                'is_enable' => 1
            ];
            $spare_part_list    = $spare_part_model->where($map)->select();
            $item['spare_part'] = [
                'spare_parts_code'           => implode(';', array_column($spare_part_list, 'spare_parts_code')),
                'spare_parts_name'           => implode(';', array_column($spare_part_list, 'spare_parts_name')),
                'spare_parts_price'          => implode(';', array_column($spare_part_list, 'spare_parts_price')),
                'spare_parts_activity_price' => implode(';', array_column($spare_part_list, 'spare_parts_activity_price')),
                'spare_parts_num'            => implode(';', array_column($spare_part_list, 'spare_parts_num')),
            ];
            $working_hours_list = $working_hours_model->where($map)->select();

            $item['working_hours'] = [
                'working_hours_code'           => implode(';', array_column($working_hours_list, 'working_hours_code')),
                'working_hours_name'           => implode(';', array_column($working_hours_list, 'working_hours_name')),
                'working_hours_price'          => implode(';', array_column($working_hours_list, 'working_hours_price')),
                'working_hours_activity_price' => implode(';', array_column($working_hours_list, 'working_hours_activity_price')),
                'working_hours_num'            => implode(';', array_column($working_hours_list, 'working_hours_num')),
            ];

            $card_list    = $card_model->where($map)->column('card_price');
            $item['card'] = $card_list;

        }
        return $list;
    }

}