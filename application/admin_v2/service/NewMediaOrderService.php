<?php


namespace app\admin_v2\service;


use app\common\model\db\DbExports;
use app\common\model\new_media\NewMediaOrderCommodity;
use app\common\model\new_media\NewMediaOrderCommodityStore;
use app\common\model\new_media\NewMediaOrderDetail;
use app\common\model\new_media\NewMediaOrderMain;
use think\Queue;

class NewMediaOrderService
{


    /**
     * 列表
     * @param $input
     * @return \think\Paginator
     * @throws \think\exception\DbException
     */
    public function getList($input, $admin_name)
    {
        $map = [];

        $order_commodity = new NewMediaOrderCommodity();

        // 新媒体商品名称
        if (!empty($input['media_commodity_name'])) {
            $detailIds   = $order_commodity->whereLike('media_commodity_name', $input['media_commodity_name'])->column('order_detail_id');
            $map['a.id'] = ['in', $detailIds];
        }

        // 订单类型
        if (!empty($input['order_type'])) {
            $map['b.order_type'] = $input['order_type'];
        }

        // 手机号码
        if (!empty($input['user_phone'])) {
            $map['b.user_phone'] = $input['user_phone'];
        }

        // 新媒体订单号
        if (!empty($input['third_order_id'])) {
            $map['b.third_order_id'] = $input['third_order_id'];
        }

        // 商城总单号
        if (!empty($input['order_main_no'])) {
            $map['b.order_main_no'] = $input['order_main_no'];
        }

        // 商城子单号
        if (!empty($input['order_detail_no'])) {
            $map['a.order_detail_no'] = $input['order_detail_no'];
        }

        // 渠道来源
        if (!empty($input['channel_source'])) {
            $channelSourceClientId = NewMediaOrderMain::$channelSourceClientId;
            $clientIdArr           = [];
            foreach ($input['channel_source'] as $channel) {
                $arr         = $channelSourceClientId[$channel] ?? [];
                $clientIdArr = array_merge($arr, $clientIdArr);
            }
            $map['b.client_id'] = ['in', $clientIdArr];
        }
        // 订单来源
        if (!empty($input['order_source'])) {
            $map['b.order_source'] = $input['order_source'];
        }


        // 订单状态
        if (!empty($input['order_status'])) {
            $map['a.order_status'] = ['in', $input['order_status']];
        }

        // 下单时间
        if (!empty($input['order_created_date'])) {
            $arr                         = explode(' ~ ', $input['order_created_date']);
            $map['b.order_created_date'] = ['between', $arr];
        }

        // 支付时间
        if (!empty($input['order_paid_time'])) {
            $arr                      = explode(' ~ ', $input['order_paid_time']);
            $map['b.order_paid_time'] = ['between', $arr];
        }

        if (isset($input['is_ajax_down']) && $input['is_ajax_down'] == 1) {

            if (empty($input['order_created_date']) && empty($input['order_paid_time'])) {
                print_json(1, '下单/支付时间必选其一');
            }

            $create_days = 0;
            $pay_days    = 0;
            if (!empty($input['order_created_date'])) {
                list($created_date_start, $created_date_end) = explode(' ~ ', $input['order_created_date']);
                $create_days = date_diff(date_create($created_date_start), date_create($created_date_end))->days;
            } else if (!empty($input['order_paid_time'])) {
                list($pay_time_start, $pay_time_end) = explode(' ~ ', $input['order_paid_time']);
                $pay_days = date_diff(date_create($pay_time_start), date_create($pay_time_end))->days;
            }

            if ($create_days > 31 || $pay_days > 31) {
                print_json(1, '下单/支付时间范围请小于1个月');
            }

            // 异步下载
            $params_key = md5(json_encode($input));

            $export = DbExports::create([
                'export_type'   => 'new_media_order',
                'filter_params' => json_encode($map),
                'export_key'    => $params_key,
                'creator'       => $admin_name
            ]);

            Queue::push('app\admin_v2\queue\NewMediaOrderQueue', json_encode([
                'map' => $map,
                'id'  => $export->id,
            ]), config('queue_type.new_media'));
            print_json(0, '已加入异步下载列表');
        }


        $per_page              = $input['per_page'] ?? 10;
        $detail_model          = new NewMediaOrderDetail();
        $field                 = 'b.client_id,b.third_order_id,b.order_type,b.order_main_no,a.order_detail_no,b.user_name,b.user_phone,
        b.order_source,a.order_status,b.card_count,a.card_sort,b.voucher_value,b.total_amount,a.actual_payment_amount,
        a.total_discount_amount,a.total_discount_amount,a.total_platform_discount_amount,a.total_mfrs_discount_amount,
        a.total_mfrs_subsidy_amount,a.store_code,c.dlr_name,b.order_created_date,b.order_paid_time,a.order_main_id,
        a.id as order_detail_id';
        $list                  = $detail_model->alias('a')
            ->join('t_new_media_order_main b', 'a.order_main_id=b.id')
            ->join('t_db_dlr c', 'a.store_code=c.dlr_code and c.is_enable=1 and brand_type=1', 'left')
            ->field($field)
            ->where($map)
            ->order('a.modified_date desc')
            ->paginate($per_page);
        $commodity_store_model = new NewMediaOrderCommodityStore();

        $clientId    = NewMediaOrderMain::$clientId;
        $orderSource = NewMediaOrderMain::$orderSource;
        $orderType   = NewMediaOrderMain::$orderType;
        $orderStatus = NewMediaOrderDetail::$orderStatus;
        foreach ($list as $key => $item) {
            $item['channel_source']    = $clientId[$item['client_id']] ?? '';
            $item['order_source_text'] = $orderSource[$item['order_source']] ?? '';
            $item['order_type_text']   = $orderType[$item['order_type']] ?? '';
            $item['order_status_text'] = $orderStatus[$item['order_status']] ?? '';
            $item['order_main_id']     = (string)$item['order_main_id'];
            $item['order_detail_id']   = (string)$item['order_detail_id'];
            $where                     = [
                'order_main_id' => $item['order_main_id']
            ];
            $num                       = $commodity_store_model->where($where)->count();
            $item['apply_store_num']   = $num;
        }
        return $list;
    }


    /**
     * 订单列表搜索配置
     * @return array
     */
    public function searchConfig()
    {
        return [
            'commodity_type' => NewMediaOrderCommodity::$commodityType,
            'channel_source' => NewMediaOrderMain::$channelSource,
            'order_source'   => NewMediaOrderMain::$orderSource,
            'order_type'     => NewMediaOrderMain::$orderType,
            'order_status'   => NewMediaOrderDetail::$orderStatus,
        ];
    }


    /**
     * 获取适用专用店
     * @param $orderMainId
     * @return array|false|string
     */
    public function getApplyToDlr($orderMainId)
    {
        $map = ['order_main_id' => $orderMainId];

        $commodity_store_model = new NewMediaOrderCommodityStore();
        return $commodity_store_model->where($map)->column('store_code');
    }


    /**
     * 订单商品
     * @param $orderDetailId
     * @return bool|\PDOStatement|string|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function orderCommodities($orderDetailId)
    {
        $map   = [
            'order_detail_id' => $orderDetailId
        ];
        $field = 'media_commodity_sku_id,media_commodity_name,media_commodity_sku_name,part_code,part_quantity,
        total_part_actual_payment,wi_code,wi_quantity,total_wi_actual_payment';

        $order_commodity_model = new NewMediaOrderCommodity();
        $list                  = $order_commodity_model->where($map)->field($field)->select();
        foreach ($list as $key => $item) {
            $list[$key]['code_type'] = 1; // 备件
            if (empty($item['part_code']) && !empty($item['wi_code'])) {
                $list[$key]['part_code']     = $item['wi_code'];
                $list[$key]['part_quantity'] = $item['wi_quantity'];
                $list[$key]['total_part_actual_payment'] = number_format($item['total_wi_actual_payment'] / 100, 2);
                $list[$key]['code_type'] = 2; // 工时
            } else {
                $list[$key]['total_part_actual_payment'] = number_format($item['total_part_actual_payment'] / 100, 2);
            }

            unset($list[$key]['wi_code']);
            unset($list[$key]['wi_quantity']);
            unset($list[$key]['total_wi_actual_payment']);
        }
        return $list;
    }


}