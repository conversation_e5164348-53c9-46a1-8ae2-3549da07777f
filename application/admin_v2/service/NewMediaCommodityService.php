<?php


namespace app\admin_v2\service;


use app\common\model\new_media\NewMediaCommodity;
use app\common\model\new_media\NewMediaCommodityCard;
use app\common\model\new_media\NewMediaCommoditySku;
use app\common\model\new_media\NewMediaCommoditySpareParts;
use app\common\model\new_media\NewMediaCommodityWorkingHours;
use app\common\model\new_media\NewMediaImport;
use app\common\model\new_media\NewMediaImportRecord;
use app\common\model\new_media\NewMediaKsCommodity;
use app\common\port\connectors\DouDian;
use app\common\return_data\JsonBuilder;
use app\common\return_data\MessageBag;
use think\Db;
use think\Exception;
use tool\PhpExcelPage;

class NewMediaCommodityService
{


    /**
     * 列表
     * @param $input
     * @return \think\Paginator
     * @throws \think\exception\DbException
     */
    public function getList($input)
    {
        $map = [];
        if (!empty($input['platform'])) {
            $map['platform'] = $input['platform'];
        }
        if (!empty($input['commodity_name'])) {
            $map['commodity_name'] = ['like', '%'.$input['commodity_name'].'%'];
        }
        if (!empty($input['commodity_id'])) {
            $map['a.commodity_id'] = $input['commodity_id'];
        }
        if (!empty($input['commodity_type'])) {
            $map['commodity_type'] = $input['commodity_type'];
        }

        $commodity_sku_model = new NewMediaCommoditySku();

        $per_page = $input['per_page'] ?? 10;

        $field = 'a.id,b.platform,b.commodity_name,b.commodity_id,a.sku_name,a.sku_id,b.commodity_type,b.type_value,
        spare_parts_type,working_hours_type,a.modified_date';

        return $commodity_sku_model->alias('a')
            ->join('t_new_media_commodity b', 'a.commodity_id=b.commodity_id')
            ->field($field)
            ->where($map)
            ->order('a.modified_date desc')
            ->paginate($per_page);
    }


    /**
     * 下载抖音商品
     * @throws \PHPExcel_Exception
     * @throws \PHPExcel_Writer_Exception
     */
    public function downDyCommodity()
    {
        $result = DouDian::create('dou_dian')->getGoodsList(1, 1);
        $total  = $result['total'];
        $limit  = 100;
        $page   = ceil($total / $limit);

        $pageExcel = new PhpExcelPage();
        $pageExcel->setSheetTitle('抖音商品拉取表');
        $title = [
            'A' => '商品名称', 'B' => '商品ID', 'C' => '商品类型', 'D' => '商品子类型', 'E' => '品类全名', 'F' => '创建时间',
            'G' => '更新时间', 'H' => 'sku_id', 'I' => 'sku名称', 'J' => 'sku状态', 'K' => '实际支付价格(元)'
        ];
        $pageExcel->setTitle($title);
        $widthArr = [
            'A' => '30', 'B' => '30', 'C' => '30', 'D' => '30', 'E' => '50', 'F' => '30',
            'G' => '30', 'H' => '30', 'I' => '30', 'J' => '50', 'K' => '30',
        ];
        $pageExcel->setWidth($widthArr);

        for ($i = 0; $i < $page; $i++) {

            $data       = [];
            $pageResult = DouDian::create('dou_dian')->getGoodsList($i + 1, $limit);
            if (!empty($pageResult['list'])) {
                foreach ($pageResult['list'] as $value) {
                    $data[] = [
                        'A' => $value['product_name'],
                        'B' => $value['product_id'],
                        'C' => $value['product_type'],
                        'D' => $value['product_sub_type'],
                        'E' => $value['category_full_name'],
                        'F' => $value['dy_create_time'],
                        'G' => $value['dy_update_time'],
                        'H' => $value['sku_id'],
                        'I' => $value['sku_name'],
                        'J' => $value['sku_status'],
                        'K' => $value['actual_amount'],
                    ];
                }
                $pageExcel->setData($data, $i + 1, $limit);

            }
        }
        $pageExcel->downloadFile('抖音商品拉取表');
    }



    public function downKsCommodity()
    {
        $ksCommodity_model = new NewMediaKsCommodity();
        $params = [
            'where' => ['is_enable'=>1],
            'order' => 'id desc',
            'field' => 'product_name,product_id,product_type,category_full_name,commodity_status,check_status,sku_id,sku_name,out_sku_id,origin_amount,actual_amount,sku_count'
        ];
        $list = $ksCommodity_model->getList($params);
        $pageExcel = new PhpExcelPage();
        $pageExcel->setSheetTitle('快手商品拉取表');
        $title = [
            'A' => '商品名称', 'B' => '商品ID', 'C' => '商品类型', 'D' => '品类全名', 'E' => '商品状态', 'F' => '商品审核状态',
            'G' => 'sku_id', 'H' => 'sku名称', 'I' => '外部sku编码', 'J' => '原价(元)', 'K' => '团购价格(元)', 'L' => '商品SKU总数'
        ];
        $pageExcel->setTitle($title);
        $widthArr = [
            'A' => '30', 'B' => '30', 'C' => '30', 'D' => '30', 'E' => '50', 'F' => '30',
            'G' => '30', 'H' => '30', 'I' => '30', 'J' => '50', 'K' => '30',  'L' => '30',
        ];
        $pageExcel->setWidth($widthArr);
        $data = [];
        foreach ($list as $value) {
            $data[] = [
                'A' => $value['product_name'],
                'B' => $value['product_id'],
                'C' => $value['product_type_text'],
                'D' => $value['category_full_name'],
                'E' => $value['commodity_status_text'],
                'F' => $value['check_status_text'],
                'G' => $value['sku_id'],
                'H' => $value['sku_name'],
                'I' => $value['out_sku_id'],
                'J' => number_format($value['origin_amount']/100, 2),
                'K' => number_format($value['actual_amount']/100, 2),
                'L' => $value['sku_count'],
            ];
        }
        $pageExcel->setData($data, 0,0);
        $pageExcel->downloadFile('快手商品拉取表');

    }


    /**
     * 导入数据
     * @param $fileData
     * @return MessageBag
     */
    public function importData($fileData)
    {
        $messageBug = new MessageBag();

        $import_model = new NewMediaImport();
        $add          = [
            'import'    => 1,
            'file_name' => $fileData['file_name'],
            'path'      => $fileData['path'],
        ];
        $importId     = $import_model->insertGetId($add);

        $data = $fileData['data'];
        unset($data[0]);
        unset($data[1]);
        unset($data[2]);
        $data = array_values($data);

        // 判断数据
        $re = $this->judgeData($data);
        if ($re['code'] == 0) {
            $import_model->where('id', $importId)->update(['remark' => $re['msg']]);
            $messageBug->setMessage($re['msg']);
            $messageBug->setCode(JsonBuilder::CODE_ERROR);
            return $messageBug;
        }

        try {

            Db::startTrans();
            // 插入数据
            $import_record_model   = new NewMediaImportRecord();
            $commodity_model       = new NewMediaCommodity();
            $commodity_sku_model   = new NewMediaCommoditySku();
            $commodity_parts_model = new NewMediaCommoditySpareParts();
            $commodity_hours_model = new NewMediaCommodityWorkingHours();
            $commodity_card_model  = new NewMediaCommodityCard();

            foreach ($data as $key => $datum) {

                $record   = [
                    'import_id' => $importId,
                    'record'    => json_encode_cn($datum),
                ];
                $recordId = $import_record_model->insertGetId($record);

                // 商品主表
                $commodity_type = 0;
                if ($datum[5] == '次卡') {
                    $commodity_type = 1;
                }
                if ($datum[5] == '团购套餐') {
                    $commodity_type = 2;
                }
                if ($datum[5] == '代金券') {
                    $commodity_type = 3;
                }

                $commodity = [
                    'platform'        => $datum[0], // 新媒体
                    'commodity_name'  => $datum[1], // 新媒体商品名称
                    'commodity_id'    => $datum[2], // 新媒体商品ID
                    'commodity_type'  => $commodity_type,
                    'type_value'      => $datum[7] ?? '',
                    'commodity_class' => $datum[6],
                ];
                $map       = ['commodity_id' => $datum[2]]; // 新媒体商品ID
                $info      = $commodity_model->where($map)->find();
                if (empty($info)) {
                    $commodity_model->insert($commodity);
                } else {
                    $commodity_model->where('id', $info['id'])->update($commodity);
                }

                $factory_activity_ratio = '';
                if (!empty($datum[22])) {
                    $factory_activity_ratio = $this->formatFloat($datum[22]);
                }
                $card_ratio = '';
                if (!empty($datum[23])) {
                    $card_ratio = $this->formatFloat($datum[23]);
                }
                $activity_ratio = '';
                if (!empty($datum[24])) {
                    $activity_ratio = $this->formatFloat($datum[24]);
                }

                $member_ratio = '';
                if (!empty($datum[25])) {
                    $member_ratio = $this->formatFloat($datum[25]);
                }
                $integral_ratio = '';
                if (!empty($datum[26])) {
                    $integral_ratio = $this->formatFloat($datum[26]);
                }
                $voucher_ratio = '';
                if (!empty($datum[27])) {
                    $voucher_ratio = $this->formatFloat($datum[27]);
                }

                // sku
                $sku      = [
                    'commodity_id'           => $datum[2],
                    'sku_id'                 => $datum[4],
                    'sku_name'               => $datum[3],
                    'spare_parts_type'       => $datum[8] ?? '',
                    'working_hours_type'     => $datum[14] ?? '', // O
                    'spare_parts_activity'   => $datum[20] ?? '',  // U
                    'working_hours_activity' => $datum[21] ?? '', // V
                    'factory_activity_ratio' => $factory_activity_ratio, // W
                    'card_ratio'             => $card_ratio, // X
                    'activity_ratio'         => $activity_ratio, // Y
                    'member_ratio'           => $member_ratio, // Z
                    'integral_ratio'         => $integral_ratio, // AA
                    'voucher_ratio'          => $voucher_ratio, // AB
                ];

                $map      = [
                    'commodity_id' => $datum[2],
                    'sku_id'       => $datum[4],
                ];
                $sku_info = $commodity_sku_model->where($map)->find();
                if (empty($sku_info)) {
                    $commodity_sku_model->insert($sku);
                } else {
                    $commodity_sku_model->where('id', $sku_info['id'])->update($sku);
                }


                // 备件
                // 先删除
                $commodity_parts_model->where($map)->delete();
                if (!empty($datum[9])) {
                    $parts_code_arr           = explode('&', $datum[9]);
                    $count                    = count($parts_code_arr);
                    $parts_name_arr           = explode('&', $datum[10]);
                    $parts_price_arr          = explode('&', $datum[11]);
                    $parts_activity_price_arr = explode('&', $datum[12]);
                    $parts_num_arr            = explode('&', $datum[13]);

                    $spare_parts_arr = [];
                    for ($i = 0; $i < $count; $i++) {
                        $spare_parts_arr[] = [
                            'commodity_id'               => $datum[2],
                            'sku_id'                     => $datum[4],
                            'spare_parts_code'           => $parts_code_arr[$i],
                            'spare_parts_name'           => $parts_name_arr[$i],
                            'spare_parts_price'          => $parts_price_arr[$i] * 100, // 元转分
                            'spare_parts_activity_price' => $parts_activity_price_arr[$i] * 100, // 元转分
                            'spare_parts_num'            => $parts_num_arr[$i],
                        ];
                    }
                    $commodity_parts_model->insertAll($spare_parts_arr);
                }



                // 工时
                // 先删除
                $commodity_hours_model->where($map)->delete();
                if (!empty($datum[15])) {
                    $hours_code_arr           = explode('&', $datum[15]);
                    $count                    = count($hours_code_arr);
                    $hours_name_arr           = explode('&', $datum[16]);
                    $hours_price_arr          = explode('&', $datum[17]);
                    $hours_activity_price_arr = explode('&', $datum[18]);
                    $hours_num_arr            = explode('&', $datum[19]);

                    $working_hours_arr = [];
                    for ($i = 0; $i < $count; $i++) {
                        $working_hours_arr[] = [
                            'commodity_id'                 => $datum[2],
                            'sku_id'                       => $datum[4],
                            'working_hours_code'           => $hours_code_arr[$i],
                            'working_hours_name'           => $hours_name_arr[$i],
                            'working_hours_price'          => $hours_price_arr[$i] * 100, // 元转分
                            'working_hours_activity_price' => $hours_activity_price_arr[$i] * 100, // 元转分
                            'working_hours_num'            => $hours_num_arr[$i],
                        ];
                    }
                    $commodity_hours_model->insertAll($working_hours_arr);
                }


                // 次卡  AC
                // 先删除
                $commodity_card_model->where($map)->delete();
                $card = $this->cardData($datum);

                $commodity_card_model->insertAll($card);

                $import_record_model->where('id', $recordId)->update(['is_success' => 1]);

            }
            Db::commit();
        } catch (Exception $e) {
            Db::rollback();
            $msg = $e->getMessage();
            $messageBug->setCode(JsonBuilder::CODE_ERROR);
            $messageBug->setMessage($msg);
            $import_model->where('id', $importId)->update(['remark' => $msg]);
        }

        return $messageBug;
    }


    private function formatFloat($value, $precision = 2)
    {
        if (!is_numeric($value)) {
            return $value;
        }
        // 转换为浮点数并四舍五入
        $result = round((float)$value, $precision);
        // 确保返回字符串格式以避免显示问题
        return number_format($result, $precision, '.', '');    }


    /**
     * 判断数据
     * @param $data
     * @return array|int[]
     */
    private function judgeData($data)
    {
        $skuId = array_column($data, 4);
        if (hasDuplicateValues($skuId)) {
            $msg = '存在新媒体商品SKUID不唯一，请检查再导入';
            return ['code' => 0, 'msg' => $msg];
        }

        $index = 4;
        foreach ($data as $key => $datum) {
            // A 新媒体
            if (!in_array($datum[0], ['抖音', '快手', '京东'])) {
                $msg = '第' . ($key + $index) . '行新媒体字段有误，请检查再导入';
                return ['code' => 0, 'msg' => $msg];
            }

            // B 商品名称
            if (empty(trim($datum[1]))) {
                $msg = '第' . ($key + $index) . '行商品名称不能为空，请检查再导入';
                return ['code' => 0, 'msg' => $msg];
            }

            // C 商品id
            if (!is_numeric($datum[2])) {
                $msg = '第' . ($key + $index) . '行新媒体商品ID字段有误，请检查再导入';
                return ['code' => 0, 'msg' => $msg];
            }

            // D sku名称
            if (empty(trim($datum[3]))) {
                $msg = '第' . ($key + $index) . '行新媒体SKU名称字段有误，请检查再导入';
                return ['code' => 0, 'msg' => $msg];
            }


            // E skuID
            if (!is_numeric($datum[4])) {
                $msg = '第' . ($key + $index) . '行新媒体SKUID字段有误，请检查再导入';
                return ['code' => 0, 'msg' => $msg];
            }

            // F 新媒体商品类型
            if (!in_array($datum[5], ['次卡', '团购套餐', '代金券'])) {
                $msg = '第' . ($key + $index) . '行新媒体商品类型字段有误，请检查再导入';
                return ['code' => 0, 'msg' => $msg];
            }

            // G 新媒体商品分类
            if (empty(trim($datum[6]))) {
                $msg = '第' . ($key + $index) . '行新媒体商品分类字段有误，请检查再导入';
                return ['code' => 0, 'msg' => $msg];
            }

            if ($datum[5] == '次卡') {
                if (empty($datum[7])) {
                    $msg = '第' . ($key + $index) . '行次数必填，请检查再导入';
                    return ['code' => 0, 'msg' => $msg];
                }

                if (!is_numeric($datum[7]) || $datum[7] > 10) {
                    $msg = '第' . ($key + $index) . '行次数字段有误，请检查再导入';
                    return ['code' => 0, 'msg' => $msg];
                }

                // H 次数 和次卡金额
                $cardData = $this->cardData($datum);
                $num      = count($cardData);
                if ($datum[7] != $num) {
                    $msg = '第' . ($key + $index) . '行次数与填写的次数原价数量不一致，请检查再导入';
                    return ['code' => 0, 'msg' => $msg];
                }
            }

            if (empty($datum[8]) && empty($datum[14])) {
                //
                $msg = '第' . ($key + $index) . '行备件类型和工时类型不能同时为空，请检查再导入';
                return ['code' => 0, 'msg' => $msg];
            }

            // 备件类型
            if (!empty($datum[8])) {
                // I 备件类别
                if (!in_array($datum[8], ['保养', '维修'])) {
                    $msg = '第' . ($key + $index) . '行备件类别字段有误，请检查再导入';
                    return ['code' => 0, 'msg' => $msg];
                }
                // 备件编码
                if (empty($datum[9])) {
                    $msg = '第' . ($key + $index) . '行备件编码不能为空，请检查再导入';
                    return ['code' => 0, 'msg' => $msg];
                }
                // 备件名称
                if (empty($datum[10])) {
                    $msg = '第' . ($key + $index) . '行备件名称不能为空，请检查再导入';
                    return ['code' => 0, 'msg' => $msg];
                }

                // 备件原价
                if (empty($datum[11])) {
                    $msg = '第' . ($key + $index) . '行备件原价不能为空，请检查再导入';
                    return ['code' => 0, 'msg' => $msg];
                }


                // 备件活动价
                if (empty($datum[12])) {
                    $msg = '第' . ($key + $index) . '行备件活动价不能为空，请检查再导入';
                    return ['code' => 0, 'msg' => $msg];
                }

                // 备件数量
                if (empty($datum[13])) {
                    $msg = '第' . ($key + $index) . '行备件数量不能为空，请检查再导入';
                    return ['code' => 0, 'msg' => $msg];
                }

                $num_1 = substr_count($datum[9], '&');   // J
                $num_2 = substr_count($datum[10], '&');  // K
                $num_3 = substr_count($datum[11], '&');  // L
                $num_4 = substr_count($datum[12], '&');  // M
                $num_5 = substr_count($datum[13], '&');  // N
                $arr   = [$num_1, $num_2, $num_3, $num_4, $num_5];
                if (count(array_unique($arr)) != 1) {
                    $msg = '第' . ($key + $index) . '行备件信息有误，请检查再导入';
                    return ['code' => 0, 'msg' => $msg];
                }
            }


            // 工时类型
            if (!empty($datum[14])) {
                // O 工时类别
                if (!in_array($datum[14], ['保养', '维修'])) {
                    $msg = '第' . ($key + $index) . '行工时类别字段有误，请检查再导入';
                    return ['code' => 0, 'msg' => $msg];
                }

                // P 工时编码
                if (empty($datum[15])) {
                    $msg = '第' . ($key + $index) . '行工时类别不为空时，工时编码不能为空，请检查再导入';
                    return ['code' => 0, 'msg' => $msg];
                }

                // Q 工时名称
                if (empty($datum[16])) {
                    $msg = '第' . ($key + $index) . '行工时类别不为空时，工时名称不能为空，请检查再导入';
                    return ['code' => 0, 'msg' => $msg];
                }

                // R 工时原价
                if (empty($datum[17])) {
                    $msg = '第' . ($key + $index) . '行工时类别不为空时，工时原价不能为空，请检查再导入';
                    return ['code' => 0, 'msg' => $msg];
                }

                // S 工时活动价
                if (empty($datum[18])) {
                    $msg = '第' . ($key + $index) . '行工时类别不为空时，工时活动价不能为空，请检查再导入';
                    return ['code' => 0, 'msg' => $msg];
                }

                // T 工时数量
                if (empty($datum[19])) {
                    $msg = '第' . ($key + $index) . '行工时类别不为空时，工时数量不能为空，请检查再导入';
                    return ['code' => 0, 'msg' => $msg];
                }

                $num_1 = substr_count($datum[15], '&'); // P
                $num_2 = substr_count($datum[16], '&'); // Q
                $num_3 = substr_count($datum[17], '&'); // R
                $num_4 = substr_count($datum[18], '&'); // S
                $num_5 = substr_count($datum[19], '&'); // T
                $arr   = [$num_1, $num_2, $num_3, $num_4, $num_5];
                if (count(array_unique($arr)) != 1) {
                    $msg = '第' . ($key + $index) . '行工时信息有误，请检查再导入';
                    return ['code' => 0, 'msg' => $msg];
                }

            }




        }
        return ['code' => 1];
    }


    /**
     * 次卡数据
     * @param $datum
     * @return array
     */
    private function cardData($datum)
    {
        $card = [];
        if (!empty($datum[28])) {
            $card[0] = [
                'commodity_id' => $datum[2],
                'sku_id'       => $datum[4],
                'card_price'   => $datum[28] * 100, // 元转分
            ];
        }
        // AD
        if (!empty($datum[29])) {
            $card[1] = [
                'commodity_id' => $datum[2],
                'sku_id'       => $datum[4],
                'card_price'   => $datum[29] * 100, // 元转分
            ];
        }
        // AE
        if (!empty($datum[30])) {
            $card[2] = [
                'commodity_id' => $datum[2],
                'sku_id'       => $datum[4],
                'card_price'   => $datum[30] * 100, // 元转分
            ];
        }
        // AF
        if (!empty($datum[31])) {
            $card[3] = [
                'commodity_id' => $datum[2],
                'sku_id'       => $datum[4],
                'card_price'   => $datum[31] * 100, // 元转分
            ];
        }
        // AG
        if (!empty($datum[32])) {
            $card[4] = [
                'commodity_id' => $datum[2],
                'sku_id'       => $datum[4],
                'card_price'   => $datum[32] * 100, // 元转分
            ];
        }
        // AH
        if (!empty($datum[33])) {
            $card[5] = [
                'commodity_id' => $datum[2],
                'sku_id'       => $datum[4],
                'card_price'   => $datum[33] * 100, // 元转分
            ];
        }
        // AI
        if (!empty($datum[34])) {
            $card[6] = [
                'commodity_id' => $datum[2],
                'sku_id'       => $datum[4],
                'card_price'   => $datum[34] * 100, // 元转分
            ];
        }
        // AJ
        if (!empty($datum[35])) {
            $card[7] = [
                'commodity_id' => $datum[2],
                'sku_id'       => $datum[4],
                'card_price'   => $datum[35] * 100, // 元转分
            ];
        }
        // AK
        if (!empty($datum[36])) {
            $card[8] = [
                'commodity_id' => $datum[2],
                'sku_id'       => $datum[4],
                'card_price'   => $datum[36] * 100, // 元转分
            ];
        }
        // AL
        if (!empty($datum[37])) {
            $card[9] = [
                'commodity_id' => $datum[2],
                'sku_id'       => $datum[4],
                'card_price'   => $datum[37] * 100, // 元转分
            ];
        }
        return $card;
    }



    /**
     * sku详情
     * @param $skuId
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function skuInfo($skuId)
    {
        $commodity_sku_model = new NewMediaCommoditySku();
        $skuInfo             = $commodity_sku_model->where('sku_id', $skuId)->find();
        if (empty($skuInfo)) {
            return [];
        }
        $commodity_model = new NewMediaCommodity();
        $map             = ['commodity_id' => $skuInfo['commodity_id']];
        $field           = 'commodity_id, platform, commodity_name, commodity_type,commodity_class';
        $commodity_info  = $commodity_model->where($map)->field($field)->find();
        // 备件
        $spare_parts_model                     = new NewMediaCommoditySpareParts();
        $where                                 = [
            'sku_id'    => $skuId,
            'is_enable' => 1
        ];
        $spareParts                            = $spare_parts_model->where($where)->select();
        $skuInfo['spare_parts_code']           = implode(';', array_column($spareParts, 'spare_parts_code'));
        $skuInfo['spare_parts_name']           = implode(';', array_column($spareParts, 'spare_parts_name'));
        $skuInfo['spare_parts_price']          = implode(';', array_column($spareParts, 'spare_parts_price'));
        $skuInfo['spare_parts_activity_price'] = implode(';', array_column($spareParts, 'spare_parts_activity_price'));
        $skuInfo['spare_parts_num']            = implode(';', array_column($spareParts, 'spare_parts_num'));

        // 工时
        $working_hours_model                     = new NewMediaCommodityWorkingHours();
        $workingHours                            = $working_hours_model->where($where)->select();
        $skuInfo['working_hours_code']           = implode(';', array_column($workingHours, 'working_hours_code'));
        $skuInfo['working_hours_name']           = implode(';', array_column($workingHours, 'working_hours_name'));
        $skuInfo['working_hours_price']          = implode(';', array_column($workingHours, 'working_hours_price'));
        $skuInfo['working_hours_activity_price'] = implode(';', array_column($workingHours, 'working_hours_activity_price'));
        $skuInfo['working_hours_num']            = implode(';', array_column($workingHours, 'working_hours_num'));


        // 次卡
        $card_model       = new NewMediaCommodityCard();
        $cards            = $card_model->where($where)->field('card_price')->select();
        $card_price = array_column($cards,'card_price');
        $skuInfo['cards'] = $card_price;
        return [
            'commodity' => $commodity_info,
            'sku'       => $skuInfo
        ];

    }


    /**
     * 删除商品
     * @param $skuId
     * @return MessageBag
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function delCommodity($skuId)
    {
        $messageBag = new MessageBag();

        $sku_model           = new NewMediaCommoditySku();
        $spare_parts_model   = new NewMediaCommoditySpareParts();
        $working_hours_model = new NewMediaCommodityWorkingHours();
        $card_model          = new NewMediaCommodityCard();
        $commodity_model     = new NewMediaCommodity();
        $map                 = ['sku_id' => $skuId];

        $skuInfo = $sku_model->where($map)->find();
        if (empty($skuInfo)) {
            $messageBag->setMessage('已删除');
            return $messageBag;
        }
        try {
            Db::startTrans();
            $sku_model->where($map)->delete();
            $spare_parts_model->where($map)->delete();
            $working_hours_model->where($map)->delete();
            $card_model->where($map)->delete();
            // 判断该商品是否还有sku
            $num = $sku_model->where('commodity_id', $skuInfo['commodity_id'])->count();
            if ($num == 0) {
                $commodity_model->where('commodity_id', $skuInfo['commodity_id'])->delete();
            }


            Db::commit();
        } catch (Exception $e) {
            Db::rollback();
            $msg = $e->getMessage();
            $messageBag->setMessage($msg);
            $messageBag->setCode(JsonBuilder::CODE_ERROR);
        }
        return $messageBag;
    }


}