<?php


namespace app\admin_v2\service;


use app\common\model\db\DbDlr;
use app\common\model\new_media\NewMediaDlr;
use app\common\model\new_media\NewMediaImport;
use app\common\model\new_media\NewMediaImportRecord;
use app\common\return_data\JsonBuilder;
use app\common\return_data\MessageBag;
use think\Db;
use think\Exception;

class NewMediaDlrService
{


    /**
     * 列表
     * @param $input
     * @return \think\Paginator
     * @throws \think\exception\DbException
     */
    public function getPageList($input)
    {
        $map = ['is_enable'=>1];
        if (!empty($input['platform'])) {
            $map['platform'] = $input['platform'];
        }
        if (!empty($input['dlr_id'])) {
            $map['dlr_id'] = $input['dlr_id'];
        }
        if (!empty($input['dfn_dlr_code'])) {
            $map['dfn_dlr_code'] = $input['dfn_dlr_code'];
        }
        $dlr_model = new NewMediaDlr();
        $per_page  = $input['per_page'] ?? 10;
        $field = 'platform, dlr_id, dfn_dlr_code, dfn_dlr_name';
        return $dlr_model->where($map)->field($field)->order('modified_date desc')->paginate($per_page);
    }


    /**
     * 导入数据
     * @param $fileData
     * @return MessageBag
     */
    public function importData($fileData)
    {
        $messageBug   = new MessageBag();
        $import_model = new NewMediaImport();
        $add          = [
            'import'    => 2,
            'file_name' => $fileData['file_name'],
            'path'      => $fileData['path'],
        ];
        $importId     = $import_model->insertGetId($add);
        $data         = $fileData['data'];
        unset($data[0]);
        unset($data[1]);
        $data = array_values($data);
        // 判断数据
        $re = $this->judgeData($data);
        if ($re['code'] == 0) {
            $import_model->where('id', $importId)->update(['remark' => $re['msg']]);
            $messageBug->setMessage($re['msg']);
            $messageBug->setCode(JsonBuilder::CODE_ERROR);
            return $messageBug;
        }
        $dlr_model  = new DbDlr();
        $dfnDlrCode = array_column($data, 2);
        $map        = [
            'brand_type' => 1, // 日产
            'dlr_code'   => ['in', $dfnDlrCode],
            'is_enable'  => 1,
        ];
        $dlrNameArr = $dlr_model->where($map)->column('dlr_name', 'dlr_code');

        $new_dlr_model       = new NewMediaDlr();
        $import_record_model = new NewMediaImportRecord();
        try {
            Db::startTrans();
            foreach ($data as $datum) {

                $record   = [
                    'import_id' => $importId,
                    'record'    => json_encode_cn($datum),
                ];
                $recordId = $import_record_model->insertGetId($record);

                $map  = ['dlr_id' => $datum[1]];
                $info = $new_dlr_model->where($map)->find();

                if (empty($info)) {
                    // 新增
                    $add = [
                        'platform'     => $datum[0],
                        'dlr_id'       => $datum[1],
                        'dfn_dlr_code' => $datum[2],
                        'dfn_dlr_name' => $dlrNameArr[$datum[2]] ?? ''
                    ];
                    $new_dlr_model->insert($add);
                } else {
                    // 更新
                    $upd = [
                        'platform'     => $datum[0],
                        'dlr_id'       => $datum[1],
                        'dfn_dlr_code' => $datum[2],
                        'dfn_dlr_name' => $dlrNameArr[$datum[2]] ?? ''
                    ];
                    $new_dlr_model->where('id', $info['id'])->update($upd);
                }

                $import_record_model->where('id', $recordId)->update(['is_success' => 1]);
            }
            Db::commit();

        } catch (Exception $e) {
            Db::rollback();
            $msg = $e->getMessage();
            $messageBug->setCode(JsonBuilder::CODE_ERROR);
            $messageBug->setMessage($msg);
            $import_model->where('id', $importId)->update(['remark' => $msg]);
        }
        return $messageBug;
    }


    /**
     * 判断数据
     * @param $data
     * @return array|int[]
     */
    private function judgeData($data)
    {

        foreach ($data as $key => $datum) {
            // A 新媒体平台
            if (!in_array($datum[0], ['抖音', '快手', '京东'])) {
                $msg = '第' . ($key + 3) . '行新媒体平台有误，请检查再导入';
                return ['code' => 0, 'msg' => $msg];
            }

            // B 新媒体门店ID
            if (empty($datum[1])) {
                $msg = '第' . ($key + 3) . '行新媒体门店ID必填，请检查再导入';
                return ['code' => 0, 'msg' => $msg];
            }

            // C 新媒体门店ID
            if (empty($datum[2])) {
                $msg = '第' . ($key + 3) . '行DFN门店编码必填，请检查再导入';
                return ['code' => 0, 'msg' => $msg];
            }
        }

        $dlrId = array_column($data, 1);
        if (hasDuplicateValues($dlrId)) {
            $msg = '存在新媒体门店ID不唯一，请检查再导入';
            return ['code' => 0, 'msg' => $msg];
        }
        $dfnDlrCode = array_column($data, 2);
        if (hasDuplicateValues($dfnDlrCode)) {
            $msg = '存在DFN门店编码不唯一，请检查再导入';
            return ['code' => 0, 'msg' => $msg];
        }

        $new_media_dlr_model = new NewMediaDlr();
        $num = $new_media_dlr_model->whereIn('dfn_dlr_code', $dfnDlrCode)->count();
        if ($num > 0) {
            $msg = '有已存在DFN门店编码，请检查再导入';
            return ['code' => 0, 'msg' => $msg];
        }


        $dlr_model = new DbDlr();
        $map       = [
            'brand_type' => 1, // 日产
            'dlr_code'   => ['in', $dfnDlrCode],
            'is_enable'  => 1,
        ];
        $dlr_num   = $dlr_model->where($map)->count();
        if (count($dfnDlrCode) > $dlr_num) {
            $msg = 'DFN门店编码输入有误，请检查再导入';
            return ['code' => 0, 'msg' => $msg];
        }



        return ['code' => 1];

    }


    /**
     * 删除
     * @param $dlrId
     * @return MessageBag
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function delDlr($dlrId)
    {
        $messageBag = new MessageBag();

        $dlr_model = new NewMediaDlr();
        $info = $dlr_model->where('dlr_id', $dlrId)->find();
        if (empty($info)) {
            $messageBag->setMessage('已删除');
            return $messageBag;
        }
        $re = $dlr_model->where('id', $info['id'])->delete();
        if (!$re) {
            $messageBag->setCode(JsonBuilder::CODE_ERROR);
            $messageBag->setMessage('删除失败');
        }
        return $messageBag;
    }

}