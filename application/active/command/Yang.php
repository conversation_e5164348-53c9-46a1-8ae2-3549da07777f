<?php


namespace app\active\command;


use think\Config;
use think\console\Command;
use think\console\Input;
use think\console\Output;

class Yang extends Command
{

    protected function configure()
    {
        $this->setName('yang')->setDescription('修复满减补贴金额数据');
    }


    protected function execute(Input $input, Output $output)
    {
        Config::load(ROOT_PATH . 'config/config.php');
        Config::load(ROOT_PATH . 'config/' . config('app_status') . '.php');


    }
}
