<?php
/**
 * Created by <PERSON><PERSON><PERSON>tor<PERSON>.
 * User: llj
 * Date: 2021/8/21
 * Time: 8:51 AM
 */

namespace app\net_small\controller;

use app\common\model\act\AcShare211212;
use app\common\model\bu\BuCardReceiveRecord;
use app\common\model\db\DbActivity;
use app\common\model\db\DbActivityCard;
use app\common\model\db\DbActivityCenterLog;
use app\common\model\db\DbDlr;
use app\common\model\db\DbSendCardPage;
use app\common\model\db\DbUser;
use app\common\model\db\DbUserCarSeries;
use app\common\net_service\NetActivityCenter;
use app\common\net_service\NetCard;
use app\common\net_service\NetGoods;
use app\common\net_service\NetUser;
use app\common\validate\Card as CardValidate;
use think\exception\HttpResponseException;
use app\common\model\db\DbCard;
use app\common\model\bu\BuQyPoster;
use tool\Logger;

class Card extends Common
{

    public function __construct()
    {
        parent::__construct();
        if (!$this->user_id) {
            $response = $this->setResponseError('请登录!', 401)->send();
            throw new HttpResponseException($response);
        }
    }

    /**
     * 海报扫码进入页
     * */
    /**
     * 海报扫码进入页
     * */
    public function getCardPoster(CardValidate $validate)
    {
//        $this->user_vin = "LGBR4DE41HR045172";
//        $this->user['vin'] = 'LGBR4DE41HR045172';
        //dd($this->user_id);
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("card_poster")->check($requestData);
        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }

        $poster_id         = $requestData['poster_id'];
        $poster_model      = new BuQyPoster();
        $card_model        = new DbCard();
        $card_record_model = new BuCardReceiveRecord();
        $dlr_model         = new DbDlr();
        $send_card_model   = new DbSendCardPage();
        $dlr               = $dlr_model->getOneRecord(['dlr_code' => $this->channel_type]);
        $poster            = $poster_model->getOne(['where' => ['id' => $poster_id], 'dlr_code' => $this->channel_type]);
        if (!$poster) {
            return $this->setResponseError('无海报信息')->send();
        }
        $send_params = [
            'field' => 'a.poster_img, b.card_list',
            'where' => [
                'a.id'     => $poster['send_card_page_id'],
                'b.module' => 3,
                'b.card_list' => ['neq', '[]']
            ],
        ];
        $card_page   = $send_card_model->getSendMsg($send_params);
        //echo $send_card_model->getLastSql();exit;
        $poster_msg = json_decode($poster['card_list'], true);
        $card_msg   = json_decode($card_page['card_list'], true);
        $card_ids   = array_keys($poster_msg);

        //卡券id找到活动id去调活动中心的 商城活动可领取卡券接口
        $dbActivityCardObj = new DbActivityCard();
        $nowDate = date("Y-m-d H:i:s",time());
        $activityCardList = $dbActivityCardObj->alias("a")
            ->join("t_bu_card_receive_record b","a.card_id=b.card_id")
            ->join("t_db_card c","a.card_id=c.id")
            ->where(["a.is_enable"=>1,"b.is_enable"=>1,"c.is_enable"=>1,"b.user_id"=>$this->user_id])
            ->whereIn('a.card_id',$card_ids)
            ->whereIn("c.act_status",[1,2])
            ->where('b.validity_date_start <="'. $nowDate. '" AND b.validity_date_end >="'. $nowDate.'"')
            ->field("a.activity_id,a.card_id")->select();
       // echo $dbActivityCardObj->getLastSql();exit;
        $activityCouponData = [];
        $activityIdList = [];
        $couponIdList= [] ;
        foreach($activityCardList as $activityCardItem){
            $activityIdList[] = $activityCardItem['activity_id'];
            $couponIdList[] = $activityCardItem['card_id'];
        }
        $activityCouponData['activityIdList'] = implode(',',$activityIdList);
        $activityCouponData['couponIdList'] =  implode(',',$couponIdList);

        $netActivityCenter = new NetActivityCenter();
        //调商城活动可领取卡券接口
        $card_activity_center_list = $netActivityCenter->getMallActivityCouponList($this->user,$activityCouponData);

        $cardActivityCenterIdArr = [];
        $card_activity_center_item_arr = [];
        $card_id_vin_arr = [];
        $authVinList = [];
        $defaultVin = "";
        $card_activity_center_list_ret = $card_activity_center_list['rows'] ?? [];
        if(!empty($card_activity_center_list_ret)){
            //获取车型列表中所有认证车VIN
            $activityCenterObj = new ActivityCenter();
            $have_vin_list = $activityCenterObj->getHaveVin($this->user);

            $authVinArr=[];
            $defaultVinArr=[];
            foreach($have_vin_list as $have_vin_item){
                //默认车vin
                if($have_vin_item['is_vin_car'] == 1){
                    $defaultVinArr[$have_vin_item['vin']] = $have_vin_item;
                    $defaultVin = $have_vin_item['vin'];
                }else{//认证车vin
                    $authVinArr[$have_vin_item['vin']] = $have_vin_item;
                    $authVinList[] = $have_vin_item['vin'];
                }
            }

            foreach($card_activity_center_list_ret as $card_activity_center_item){
                $cardActivityCenterIdArr[] = $card_activity_center_item['couponId'];

                $card_activity_center_item['have_vin_equal_card_vin'] = 0;
                if(!empty($card_activity_center_item['vinIntersection'])){
                    if(array_intersect($authVinList,$card_activity_center_item['vinIntersection'])){
                        $card_activity_center_item['have_vin_equal_card_vin'] = 1; //但车型列表中有认证车VIN=发券VIN
                    }
                }
                $card_activity_center_item_arr[$card_activity_center_item['couponId']] = $card_activity_center_item;
                foreach($card_activity_center_item['receiveDataList'] as $receiveDataItem){
                    $card_id_vin_arr[$card_activity_center_item['couponId']][$receiveDataItem['vin']] = $receiveDataItem;
                }
            }

            $card_list = $card_model->getList(['where' => ['quick_win_card_id' => ['in', $cardActivityCenterIdArr], 'is_enable' => 1], 'field' => 'card_desc,id,card_name,card_type,card_quota,card_discount,least_type,least_cost,validity_date_start,validity_date_end,date_type,fixed_term,fixed_begin_term,default_detail,use_des,apply_des,not_apply_des']);
            $card_data = [];
            foreach ($card_list as $k => $v) {
                if ($v['least_cost'] > 0) {
                    $v['word'] = sprintf("满%s使用", $v['least_cost']);
                } else {
                    $v['word'] = sprintf("限购部分商品");
                }
                $get_count                                    = $card_record_model->where(['user_id' => $this->user_id, 'card_id' => $v['id'], 'poster_id' => $poster_id])->count();
                $v['can_num']                                 = $card_msg[$v['id']] - $get_count;
                $v['dlr_name']                                = $dlr['dlr_name'];
                $card_activity_center_item = $card_id_vin_arr[$v['id']][$this->user_vin] ?? [];
                //券VIN=当前默认车VIN  default_vin_equal_car_vin = 1 反之 default_vin_equal_car_vin=0
                $v['default_vin_equal_card_vin'] = 0;
                if(!empty($card_activity_center_item)){
                    //当前默认车VIN存在于券vin中
                    if($defaultVin == $card_activity_center_item['vin'] ){
                        $v['default_vin_equal_card_vin'] = 1; //1 券VIN=当前默认车VIN 0 券VIN≠当前默认车VIN
                        $v['article'] = "本券适用您车辆：" .$defaultVinArr[$defaultVin]['car_type_name'].$this->user['vin'];
                    }
                }

                //当券VIN≠当前默认车VIN，但车型列表中有认证车VIN=发券VIN
                $v['have_vin_equal_card_vin'] = 0;
                if($card_activity_center_item_arr[$v['id']]['have_vin_equal_card_vin'] == 1) {
                    $v['have_vin_equal_card_vin'] = 1;//1 车型列表中有认证车VIN=券VIN 0 车型列表中有认证车VIN≠券VIN
                    $v['article'] = "本券适用您车辆：" .$authVinArr[$card_activity_center_item['vin']]['car_type_name'].$card_activity_center_item['vin']." 当前默认车辆为:".$defaultVinArr[$defaultVin]['car_type_name'].$defaultVin;
                }

                //可领取数据改为活动中心的$card_id_vin_arr
                $v['available_count']  = $card_activity_center_item['canReceive'] ?? 0 ;
                //雄说与活动中心可领取数比哪个小就要哪个
                $v['can_num'] = $v['can_num'] <  $v['available_count'] ? $v['can_num'] : $v['available_count'];
                $v['sort'] = $card_activity_center_item['received'] ?? 0 ;
                // 领取的张数
                $v['available_quantity'] = $card_activity_center_item['received'] ?? 0;
                $v['change_car'] = 0;//不显示切换车辆
                //券未达到领取上限，且，库存≠0，券VIN≠当前默认车VIN，但车型列表中有认证车VIN=券VIN，则展示「切换车辆」，点击后跳转到商城车型页
                if( $v['available_count'] > 0 && $v['can_num'] > 0 && $v['default_vin_equal_card_vin'] == 0 &&  $v['have_vin_equal_card_vin'] == 1){
                    $v['change_car'] = 1; //显示切换车辆
                }
                $card_data[array_search($v['id'], $card_ids)] = $v;

            }
            //二维数组为$arr，要按某个字段$field排序
            foreach ($card_data as $key => $value) {
                $field[$key] = $value['sort'];
            }
            array_multisort($field, SORT_ASC, $card_data);
            //ksort($card_data);
            if ($card_data) {
                $return_data = [
                    'card_list'  => $card_data,
                    'poster_img' => $card_page['poster_img'],
                    'poster_id'  => $poster_id,
                    'act_code'   => 'poster-' . $this->channel_type . '-' . $poster_id,
                ];
                return $this->setResponseData($return_data)->send();
            }
        }
        return $this->setResponseError([])->send();
    }

    /**
     * 领券页列表
     * @param CardValidate $validate
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function getCardListByPointCode(CardValidate $validate)
    {

//         $this->user_vin = "LGBM9AE99M1209374";
//        $this->user['vin'] = 'LGBM9AE99M1209374';
//        $this->user['id'] = 3688654;
//        $this->user_id = 3688654;
//        $this->user['one_id'] = '866e0111a66244fbb7cb9d54ac5426c27389';
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("card_list_by_point_code")->check($requestData);

//        $dbActivityCenterLog =  new DbActivityCenterLog();
//        $logData=[
//            'activity_id'=>'',
//            'request_id'=>"getCardListByPointCode",
//            'oneid'=>'',
//            'request_url'=>"",
//            'request_info'=>json_encode_cn($this->user),
//            'response_info'=>json_encode_cn($requestData)
//        ];
//        $dbActivityCenterLog->insertGetId($logData);


      //  dd($this->user);
        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }

        $page              = $requestData['page'] ?? 1;
        $page_size         = $requestData['page_size'] ?? 10;
        $point_code        = $requestData['point_code'] ?? '';
        $card_str_ids         = $requestData['card_str_ids'] ?? "";
        $poster_id         = $requestData['poster_id'] ?? 0;
        $status         = $requestData['status'] ?? 0; // 0全部 1可领券 2已领券 3待激活券
        if($poster_id > 0 ) $status = 1;//海报进来都是可领的
        $data = [
            'card_str_ids'=>$card_str_ids,
            'poster_id'=>$poster_id,
            'point_code'=>$point_code,
            'status'=>$status,
            'channel_type'=>$this->channel_type
        ];

        $netCard = new NetCard();
//        $loot_all_card_list = $netCard->getLootAllCard($this->user,$data);
//        return $this->setResponseData($loot_all_card_list)->send();
        $can_card_data = [];
       // $have_card_data = $netCard->getHaveCard($this->user,$data);
       // dd($status);
        if($status == 2){
            $card_data = $netCard->getHaveCard($this->user,$data,$status);
            //已领券页不反正抢光券
            foreach($card_data as $kkk=>$item){
                if($item['have_get'] <= 0 && $item['available_count'] <= 0 ){
                    unset($card_data[$kkk]);
                }
            }
            $card_data = $this->getSortBykey($card_data);
        }elseif($status == 1){
            $card_data_list = $netCard->getUsablecard($this->user,$data,$status);
            $card_data = $this->getSortBykey($card_data_list['get_card_list']);
        }elseif($status == 3){
            // 新增：获取待激活券
            $card_data = $this->getPendingActivationCards($this->user, $data);
            $card_data = $this->getSortBykey($card_data);
        }else{
            $card_data_list = $netCard->getUsablecard($this->user,$data,$status);

            $i = 0;
            foreach($card_data_list['get_card_list'] as $have_card_item){
                $have_card_item['is_card_use'] = 0;
                $can_card_data[$i++] = $have_card_item;
            }
            $have_card_data = $netCard->getHaveCard($this->user,$data,$status);
            foreach($have_card_data as $have_card_itema){
                $can_card_data[$i++] = $have_card_itema;
            }

            $card_data  = $this->getSortBykey($can_card_data);
        }

        $dbDlrObj = new DbDlr();
        $dlrList = $dbDlrObj->where(['is_enable'=>1,'brand_type'=>$this->user['brand']])->field('dlr_code,dlr_name')->select();
        $dlrArr = [];
        foreach($dlrList as $dlrItem){
            $dlrArr[$dlrItem['dlr_code']] = $dlrItem['dlr_name'];
        }

        if(!empty($card_data)){
            $new_card_data = [];
            foreach($card_data as $kl=>$card_item){
                $card_item['apply_dlr_name'] = '';
                if(!empty($card_item['apply_dlr_code'])){
                   $apply_dlr_code_arr = explode(',',$card_item['apply_dlr_code']);
                   foreach($apply_dlr_code_arr as $item){
                       $dlrName =  $dlrArr[$item] ?? '';
                       $card_item['apply_dlr_name'] .=  $dlrName." ";
                   }

                }else{
                    $card_item['apply_dlr_name'] = 'Ni+商城';
                }
                if ($card_item['least_cost'] > 0) {
                    $card_item['word'] = sprintf("满%s使用", $card_item['least_cost']);
                } else {
                    $card_item['word'] = sprintf("限购部分商品");
                }
                $card_item['id'] = strval($card_item['id']);
                $card_item['card_id'] = strval($card_item['card_id']);
               // $card_item['card_discount'] = sprintf("%.1f", $card_item['card_discount']);
                $kb = $kl+1;
                $card_item['card_name'] = $card_item['card_name'];
                $new_card_data[] = $card_item;
            }

            if(empty($poster_id)) $poster_id = "getCardListByPointCode";
            //只有发券页不用分页
            if($point_code != 'ZhuanYingDianFaQuanYe'){
                $card_data = $this->myPaginate($new_card_data, $page, $page_size);
            }

            $return_data = [
                'card_list'  => $card_data,
                'poster_img' => $card_page['poster_img'] ?? '',
                'poster_id'  => $poster_id,
                'act_code'   => 'poster-code-' . $this->channel_type . '-' . $poster_id,
                'page' =>$page,
                'record' => count($new_card_data),
                'page_size'=>$page_size,
                'sum_page'=>ceil(count($new_card_data) / $page_size)
            ];
            return $this->setResponseData($return_data)->send();

        }else{
            return $this->setResponseData([])->send();

        }
    }

    function getSortBykey($data){
        //以下主要处理可领抢光，但已经又出现了
        $out_arr = [];
        foreach($data as $nn=>$item){
            if($item['sort'] == 1){
                if(in_array($item['card_id'],$out_arr)){
                    unset($data[$nn]);
                }
                $out_arr[] = $item['card_id'];
            }
        }


        //排序
        usort($data, function($a, $b) {
            if ($a['isHaveGet'] != $b['isHaveGet']) {
                return $a['isHaveGet'] < $b['isHaveGet'] ?  1 : -1 ;
            }
            if ($a['sort'] != $b['sort']) {
                return $a['sort'] < $b['sort'] ?  1 : -1 ;
            }
            return $a['created_date'] < $b['created_date'] ?  1 : -1 ;
        });
        return $data;
    }


    function myPaginate($array, $page_number, $page_size) {
        // 计算要跳过的元素数量
        $offset = ($page_number - 1) * $page_size;
        // 提取指定范围的元素
        if(empty($array)){
            return [];
        }else{
            return array_slice($array, $offset, $page_size);
        }
    }


//    public function getCardPoster(CardValidate $validate)
//    {
//        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
//        $result      = $validate->scene("card_poster")->check($requestData);
//        //校验失败,返回异常
//        if (empty($result)) {
//            return $this->setResponseError($validate->getError())->send();
//        }
//
//        $poster_id         = $requestData['poster_id'];
//        $poster_model      = new BuQyPoster();
//        $card_model        = new DbCard();
//        $card_record_model = new BuCardReceiveRecord();
//        $dlr_model         = new DbDlr();
//        $send_card_model   = new DbSendCardPage();
//        $dlr               = $dlr_model->getOneRecord(['dlr_code' => $this->channel_type]);
//        $poster            = $poster_model->getOne(['where' => ['id' => $poster_id], 'dlr_code' => $this->channel_type]);
//        if (!$poster) {
//            return $this->setResponseError('无海报信息')->send();
//        }
//        $send_params = [
//            'field' => 'a.poster_img, b.card_list',
//            'where' => [
//                'a.id'     => $poster['send_card_page_id'],
//                'b.module' => 3,
//            ],
//        ];
//        $card_page   = $send_card_model->getSendMsg($send_params);
//
//        $poster_msg = json_decode($poster['card_list'], true);
//        $card_msg   = json_decode($card_page['card_list'], true);
//        $card_ids   = array_keys($poster_msg);
//
//        $card_list = $card_model->getList([
//                                              'where' => ['id' => ['in', $card_ids], 'is_enable' => 1],
//                                              'field' => 'id,card_name,card_type,card_quota,card_discount,least_type,least_cost,validity_date_start,validity_date_end,date_type,fixed_term,fixed_begin_term,available_count,default_detail,use_des,apply_des,not_apply_des'
//                                          ]);
//        $card_data = [];
//        foreach ($card_list as $v) {
//            $get_all_count   = $card_record_model->where(['card_id' => $v['id'], 'poster_id' => $poster_id])->count();
//            $available_count = $poster_msg[$v['id']] - $get_all_count;
//            if ($v['available_count'] > $available_count) {
//                $v['available_count'] = $available_count;
//            }
//            if ($v['least_cost'] > 0) {
//                $v['word'] = sprintf("满%s使用", $v['least_cost']);
//            } else {
//                $v['word'] = sprintf("限购部分商品");
//            }
//            $get_count                                    = $card_record_model->where(['user_id' => $this->user_id, 'card_id' => $v['id'], 'poster_id' => $poster_id])->count();
//            $v['can_num']                                 = $card_msg[$v['id']] - $get_count;
//            $v['dlr_name']                                = $dlr['dlr_name'];
//            $card_data[array_search($v['id'], $card_ids)] = $v;
//        }
//        ksort($card_data);
//        if ($card_data) {
//            $return_data = [
//                'card_list'  => $card_data,
//                'poster_img' => $card_page['poster_img'],
//                'poster_id'  => $poster_id,
//                'act_code'   => 'poster-' . $this->channel_type . '-' . $poster_id,
//            ];
//            return $this->setResponseData($return_data)->send();
//        } else {
//            return $this->setResponseError([])->send();
//        }
//    }

    /**
     * 助力页
     * */
    public function helpMsg(CardValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("help_msg")->check($requestData);
        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        $data  = [
            'is_help'   => 0,
            'status'    => 1, // 助力中
            'initiator' => null,
            'help_list' => null,
        ];
        $carer = $this->_getCarer($this->user['bind_unionid'], $this->user['plat_id']);
        if (!$carer || $carer['member_type'] <> 1){
            $data['status'] = 7;
            return $this->setResponseData($data)->send();
        }

        $time       = time();

        if (config('app_status') == 'develop'){
            $card_id = 21261;
            $start_date = '2022-04-25 00:00:00';
        }else{
            $card_id = 1287;
            $start_date = '2022-05-01 00:00:00';
        }
        $end_date   = '2022-05-30 23:59:59';
        $share_model = new AcShare211212();
        $help_id     = $requestData['help_id'] ?? 0;
        if ($help_id == 0) {
            $share_info = $share_model->getOne(['where' => ['user_id' => $this->user_id, 'is_initiator' => 1, 'card_id' => $card_id]]);
            if ($time < strtotime($start_date)) {
                $data['status'] = 5; // 活动未开始
            } elseif ($time > strtotime($end_date)) {
                $data['status'] = 6; // 活动已结束
            } else {
                if ($share_info) {
                    $data['status'] = $share_info['get_status'];
                } else {
                    $share_info = [
                        'nick_name'    => $requestData['nick_name'],
                        'head_img'     => $requestData['head_img'],
                        'user_id'      => $this->user_id,
                        'dlr_code'     => $this->channel_type,
                        'card_id'      => $card_id,
                        'act_name'     => '车主助力活动',
                        'is_initiator' => 1,
                    ];
                    $id         = $share_model->insertGetId($share_info);
                    if ($id > 0) {
                        $share_info['id'] = $id;
                    } else {
                        Logger::sql($share_model->getLastSql());
                        return $this->setResponseError('发起助力失败')->send();
                    }
                }
            }
            $data['initiator'] = [
                'id'        => $share_info['id'],
                'nick_name' => $share_info['nick_name'],
                'head_img'  => $share_info['head_img'],
                'user_id'   => $share_info['user_id'],
                'card_id'   => $share_info['card_id'],
            ];

        } else {

            $share_info = $share_model->getOne(['where' => ['id' => $help_id]]);
            if ($time < strtotime($start_date)) {
                $data['status'] = 5; // 活动未开始
            } elseif ($time > strtotime($end_date)) {
                $data['status'] = 6; // 活动已结束
            } else {
                $data['status'] = $share_info['get_status'];
                if ($share_info['is_initiator'] != 1) {
                    return $this->setResponseError('助力信息错误')->send();
                }
                if ($share_info['user_id'] != $this->user_id) {
                    if ($share_model->getOne(['where' => ['user_id' => $this->user_id, 'is_initiator' => 0, 'help_id' => $help_id]])) {
                        $data['status'] = 3; // 已经为此活动发起人助力过
                    }else {
                        $help_count = $share_model->where(['user_id' => $this->user_id, 'is_initiator' => 0, 'card_id' => $card_id])->count();
                        if ($help_count >= 1) {
                            $data['status'] = 4; // 助力次数达到上限
                        }
                    }
                    $data['is_help'] = 1; // 助力人
                }
                $data['initiator'] = [
                    'id'        => $share_info['id'],
                    'nick_name' => $share_info['nick_name'],
                    'head_img'  => $share_info['head_img'],
                    'user_id'   => $share_info['user_id'],
                    'card_id'   => $share_info['card_id'],
                ];
            }
        }

        $help_list = $share_model->getList(['where' => ['help_id' => $share_info['id']], 'field' => 'user_id,nick_name,head_img']);
        if ($help_list) {
            $data['help_list'] = $help_list;
        }
        return $this->setResponseData($data)->send();
    }

    /**
     * 助力
     * */
    public function helpInit(CardValidate $validate)
    {
        $carer = $this->_getCarer($this->user['bind_unionid'], $this->user['plat_id']);
        if (!$carer || $carer['member_type'] <> 1){
            return $this->setResponseError('参加活动需先完成车主认证')->send();
        }
        $time       = time();
        if (config('app_status') == 'develop'){
            $card_id = 21261;
            $start_date = '2022-04-25 00:00:00';
        }else{
            $card_id = 1287;
            $start_date = '2022-05-01 00:00:00';
        }
        $end_date   = '2022-05-30 23:59:59';
        if ($time < strtotime($start_date)) {
            return $this->setResponseError('活动未开始')->send();
        } elseif ($time > strtotime($end_date)) {
            return $this->setResponseError('活动已结束')->send();
        }
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("help_init")->check($requestData);
        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        $help_id = $requestData['help_id'];
        $data    = [
            'nick_name' => $requestData['nick_name'],
            'head_img'  => $requestData['head_img'],
            'user_id'   => $this->user_id,
            'dlr_code'  => $this->channel_type,
            'card_id'   => $card_id,
            'act_name'  => '车主助力活动',
        ];

        $share_model = new AcShare211212();

        $share_info = $share_model->getOne(['where' => ['id' => $help_id]]);

        if (!$share_info || $share_info['is_initiator'] != 1) {
            return $this->setResponseError('助力信息错误')->send();
        }
        if ($share_info['get_status'] == 2) {
            return $this->setResponseError('该助力已完成')->send();
        }
        if ($share_info['user_id'] == $this->user_id) {
            return $this->setResponseError('不能为自己助力')->send();
        }
        if ($share_model->getOne(['where' => ['user_id' => $this->user_id, 'is_initiator' => 0, 'help_id' => $help_id]])) {
            return $this->setResponseError('不能重复助力')->send();
        }

        $help_count = $share_model->where(['user_id' => $this->user_id, 'is_initiator' => 0, 'card_id' => $card_id])->count();
        if ($help_count >= 1) {
            return $this->setResponseError('助力次数达到上限')->send();
        }

        $data['help_id'] = $help_id;

        $status = false;
        $share_model->startTrans();
        $id = $share_model->insertGetId($data);
        if ($id > 0) {
            $status    = true;
            # 2022-05-01活动修改为一人助力即可领券
//            $count_now = $share_model->where(['help_id' => $help_id])->count();
//            if ($count_now == 2) {
                $user_model           = new DbUser();
                $user                 = $user_model->getOne(['where' => ['id' => $share_info['user_id']]]);
                $user['bind_unionid'] = $user['unionid'];

                $net_user = new NetUser();
                $res      = $net_user->get_card($data['card_id'], 'share211212-' . $help_id, 2, $user, $share_info['dlr_code']);
                if ($res['code'] == 200) {
                    $share_model->saveData(['get_status' => 2], ['id' => $help_id]);
                } else {
                    $status = false;
                }
//            }
        }
        if ($status) {
            $share_model->commit();
            return $this->setResponseData('助力成功')->send();
        } else {
            $share_model->rollback();
            Logger::sql($share_model->getLastSql());
            return $this->setResponseError('助力失败')->send();
        }
    }

    public function getHelpStatus()
    {
        if (config('app_status') == 'develop'){
            $card_id = 21261;
        }else{
            $card_id = 1287;
        }
        $share_model = new AcShare211212();
        $share_info  = $share_model->getOne(['where' => ['user_id' => $this->user_id, 'is_initiator' => 1, 'card_id' => $card_id]]);
        $status      = 1;
        if ($share_info) {
            $status = $share_info['get_status'];
        }
        return $this->setResponseData(['status' => $status, 'card_id' => $card_id])->send();
    }

    public function getCardRecINfo(CardValidate $validate)
    {
//        $this->user_vin = "LGBR4DE41HR045172";
//        $this->user['vin'] = 'LGBR4DE41HR045172';
        //dd($this->user_id);
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("card_get")->check($requestData);
        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        $act_card_id_str = $requestData['card_ids'];
        $act_card_id_arr =  explode(',',$act_card_id_str);

        $card_model        = new DbCard();
        $canGetList = $card_model->getList(['where'=>['is_enable'=>1,'id'=>['in',$act_card_id_arr]],'field'=>"*,'' as order_id",]);
        if(!$canGetList){
            return $this->setResponseData("{}")->send();
        }
        foreach($canGetList as $kk=>$activityCardItem){
            //1 可领取
            if(empty($query_card_arr_tmp[$activityCardItem['id']])){//空表示没领过
                $canGetList[$kk]['is_can_receive'] = 1;
            }else{
                $canGetList[$kk]['is_can_receive'] = $query_card_arr_tmp[$activityCardItem['id']] > 0 ? 1 : 0;
            }
            $canGetList[$kk]['card_id'] = $activityCardItem['id'];
        }
//            print_json($canGetList);
        $net_goods = new NetGoods();
        $card_list =  $net_goods->card_get_use([],[],$this->user,$this->channel_type,[],[],1,$canGetList,"","","");
        $get_card_list = $card_list['get_card_list'];
        if($get_card_list){
            foreach ($get_card_list as $g_k=>$g_v){
                $get_card_list[$g_k]['card_quota'] =  formatNumber($g_v['card_quota']);
                $get_card_list[$g_k]['card_discount'] =  formatNumber($g_v['card_discount']);
            }
        }
        return $this->setResponseData($get_card_list)->send();

    }

    /**
     * 获取待激活券列表
     * @param array $user 用户信息
     * @param array $data 查询参数
     * @return array
     */
    private function getPendingActivationCards($user, $data)
    {
        $card_r_model = new BuCardReceiveRecord();

        // 构建查询条件
        $where = [
            'a.user_id' => $user['id'],
            'a.status' => 7, // 待激活状态
            'a.is_enable' => 1,
            'b.is_enable' => 1,
            'b.brand_id' => $user['brand']
        ];

        // 如果指定了卡券ID
        if (!empty($data['card_str_ids'])) {
            $card_str_arr = explode(',', $data['card_str_ids']);
            $where['b.id'] = ['in', $card_str_arr];
        }

        // 查询待激活券
        $pending_cards = $card_r_model->alias('a')
            ->join('t_db_card b', 'a.card_id = b.id')
            ->join('t_db_activity c', 'b.activity_id = c.activity_id', 'left')
            ->join('t_db_activity_card d', 'b.activity_id = d.activity_id AND b.id = d.card_id', 'left')
            ->where($where)
            ->where('FIND_IN_SET("' . $user['channel_type'] . '", b.up_down_channel_dlr)')
            ->field('a.id as record_id, a.receive_vin, a.validity_date_start, a.validity_date_end,
                     b.id, b.card_id, b.card_name, b.card_type, b.card_quota, b.card_discount,
                     b.least_cost, b.card_desc, b.is_gift_card, b.apply_dlr_code,
                     c.activity_id, c.activity_name,
                     d.coupon_activate_scene_list')
            ->order('a.created_date desc')
            ->select();

        if (empty($pending_cards)) {
            return [];
        }

        $result = [];
        foreach ($pending_cards as $card) {
            $card_item = [
                'id' => strval($card['id']),
                'card_id' => strval($card['card_id']),
                'card_name' => $card['card_name'],
                'card_type' => $card['card_type'],
                'card_quota' => $card['card_quota'],
                'card_discount' => $card['card_discount'],
                'least_cost' => $card['least_cost'],
                'card_desc' => $card['card_desc'],
                'is_gift_card' => $card['is_gift_card'],
                'apply_dlr_code' => $card['apply_dlr_code'],
                'activity_id' => $card['activity_id'],
                'activity_name' => $card['activity_name'],
                'coupon_activate_scene_list' => $card['coupon_activate_scene_list'],
                'status' => 7, // 待激活状态
                'record_id' => $card['record_id'],
                'receive_vin' => $card['receive_vin'],
                'validity_date_start' => $card['validity_date_start'],
                'validity_date_end' => $card['validity_date_end'],
                // 新增字段
                'is_pending_activation' => true,
                'show_switch_vehicle' => false, // 是否显示切换车辆按钮
                'activation_scenes' => [], // 激活场景信息
                'activation_button_text' => '去激活'
            ];

            // 判断是否显示切换车辆按钮
            $card_item['show_switch_vehicle'] = $this->shouldShowSwitchVehicle($card, $user);

            // 解析激活场景
            $common_service = new \app\common\net_service\Common();
            $card_item['activation_scenes'] = $common_service->parseActivationScenes($card['coupon_activate_scene_list']);

            if ($card_item['least_cost'] > 0) {
                $card_item['word'] = sprintf("满%s使用", $card_item['least_cost']);
            } else {
                $card_item['word'] = "限购部分商品";
            }

            $result[] = $card_item;
        }

        return $result;
    }

    /**
     * 判断是否显示切换车辆按钮
     * @param array $card 卡券信息
     * @param array $user 用户信息
     * @return bool
     */
    private function shouldShowSwitchVehicle($card, $user)
    {
        // 如果券的VIN与用户当前默认车VIN不一致，显示切换车辆按钮
        $current_vin = $user['vin'] ?? '';
        $card_vin = $card['receive_vin'] ?? '';

        return !empty($card_vin) && !empty($current_vin) && $card_vin !== $current_vin;
    }



    /**
     * 获取卡券激活跳转信息
     * @param CardValidate $validate
     * @return \think\Response
     */
    public function getCardActivationJump(CardValidate $validate)
    {
        $requestData = $this->request->only(['card_id', 'card_code', 'scene_code']);

        if (empty($requestData['card_id']) || empty($requestData['card_code'])) {
            return $this->setResponseError('参数缺失')->send();
        }

        $card_id = $requestData['card_id'];
        $card_code = $requestData['card_code'];
        $scene_code = $requestData['scene_code'] ?? '';

        // 查询卡券信息
        $card_r_model = new BuCardReceiveRecord();
        $card_record = $card_r_model->alias('a')
            ->join('t_db_card b', 'a.card_id = b.id')
            ->join('t_db_activity c', 'a.activity_id = c.activity_id', 'left')
            ->join('t_db_activity_card d', 'a.activity_id = d.activity_id AND b.id = d.card_id', 'left')
            ->where([
                'a.card_code' => $card_code,
                // 'a.user_id' => $this->user_id,
                'a.status' => 7, // 待激活状态
                'b.id' => $card_id
            ])
            ->field('a.*, b.activity_id, d.coupon_activate_scene_list,b.is_gift_card')
            ->find();

        if (!$card_record) {
            return $this->setResponseError('卡券不存在或已激活')->send();
        }

        // 根据激活场景返回跳转信息
        $jump_info = $this->getActivationJumpInfo($card_record, $scene_code);

        return $this->setResponseData($jump_info)->send();
    }

    /**
     * 获取激活跳转信息
     * @param array $card_record 卡券记录
     * @param string $scene_code 场景代码
     * @return array
     */
    private function getActivationJumpInfo($card_record, $scene_code)
    {
        $activity_id = $card_record['activity_id'];
        $scene_list = $card_record['coupon_activate_scene_list'];


        // 如果没有指定场景，使用第一个可用场景
        if (empty($scene_code) && !empty($scene_list)) {
            $scenes = explode(',', $scene_list);

            // 如果是买赠券(is_gift_card==1)，则场景码固定为'KeHuZaiShangChengXiaDan'
            if (isset($card_record['is_gift_card']) && $card_record['is_gift_card'] == 1) {
                $scene_code = 'KeHuZaiShangChengXiaDan';
            } else {
                // 如果不是买赠券，则可选场景必须是与[KeHuZaiShangChengXiaDan,ShangChengHeXiao]的交集
                $valid_scenes = array_intersect($scenes, ['KeHuZaiShangChengXiaDan', 'ShangChengHeXiao']);
                if (!empty($valid_scenes)) {
                    $scene_code = trim(current($valid_scenes));
                }
            }
        }

        $common_service = new \app\common\net_service\Common();
        $jump_info = [
            'jump_type' => '',
            'jump_url' => '',
            'jump_data' => [],
            'scene_name' => $common_service->getSceneName($scene_code)
        ];

        if (strpos($scene_code, 'KeHuZaiShangChengXiaDan') !== false) {
            // 订单支付场景
            $jump_info = $this->getOrderPaymentJumpInfo($activity_id);
        } elseif (strpos($scene_code, 'ShangChengHeXiao') !== false) {
            // 订单核销场景
            $jump_info = $this->getOrderConsumeJumpInfo($activity_id, $card_record);
        } else {
            // 立即购买场景
            $jump_info = $this->getImmediatePurchaseJumpInfo($activity_id);
        }

        return $jump_info;
    }

    /**
     * 获取订单支付场景跳转信息
     * @param string $activity_id 活动ID
     * @return array
     */
    private function getOrderPaymentJumpInfo($activity_id)
    {
        // 查询活动关联的商品ID
        $commodity_ids = $this->getActivityRelatedGoods($activity_id);

        $jump_info = [
            'jump_type' => 'order_payment',
            'jump_url' => '',
            'jump_data' => $commodity_ids,
            'scene_name' => '订单支付'
        ];

        // 如果只有一个商品，直接跳转到商品详情页
        if (count($commodity_ids) === 1) {
            $jump_info['jump_type'] = 'goods_detail';
//            $jump_info['jump_url'] = '/goods/detail?id=' . $commodity_ids[0];
        } else {
            // 多个商品，跳转到商品列表页
            $jump_info['jump_type'] = 'goods_list';
//            $jump_info['jump_url'] = '/goods/list?activity_id=' . $activity_id;
        }

        return $jump_info;
    }

    /**
     * 获取订单核销场景跳转信息
     * @param string $activity_id 活动ID
     * @param array $card_record 卡券记录
     * @return array
     */
    private function getOrderConsumeJumpInfo($activity_id, $card_record)
    {
        // 检查是否有已支付未核销的订单
        $unpaid_orders = $this->getUnpaidOrdersForCard($card_record['card_id']);

        $jump_info = [
            'jump_type' => 'order_consume',
            'jump_url' => '',
            'jump_data' => [],
            'scene_name' => '订单核销'
        ];

        if (!empty($unpaid_orders)) {
            // 有关联的已支付未核销订单，返回订单列表
            $jump_info['jump_type'] = 'order_list';
            $jump_info['jump_url'] = '/order/list?type=unpaid';
            $jump_info['jump_data'] = $unpaid_orders;
        } else {
            // 没有关联订单，返回活动商品ID列表
            $commodity_ids = $this->getActivityRelatedGoods($activity_id);
            $jump_info['jump_type'] = 'goods_list';
            $jump_info['jump_url'] = '/goods/list?activity_id=' . $activity_id;
            $jump_info['jump_data'] = $commodity_ids;
        }

        return $jump_info;
    }

    /**
     * 获取立即购买场景跳转信息
     * @param string $activity_id 活动ID
     * @return array
     */
    private function getImmediatePurchaseJumpInfo($activity_id)
    {
        // 查询活动关联的商品ID
        $commodity_ids = $this->getActivityRelatedGoods($activity_id);

        return [
            'jump_type' => 'immediate_purchase',
            'jump_url' => '/goods/list?activity_id=' . $activity_id,
            'jump_data' => $commodity_ids,
            'scene_name' => '立即购买'
        ];
    }

    /**
     * 获取活动关联的商品ID列表
     * @param string $activity_id 活动ID
     * @return array 商品ID数组
     */
    private function getActivityRelatedGoods($activity_id)
    {
        if (empty($activity_id)) {
            return [];
        }

        // 查询活动关联的SKU
        $activity_ass_sku_model = new \app\common\model\db\DbActivityAssSku();
        $sku_list = $activity_ass_sku_model->where([
            'activity_id' => $activity_id,
            'is_enable' => 1
        ])->select();

        if (empty($sku_list)) {
            return [];
        }

        $commodity_model = new \app\common\model\db\DbCommodity();
        $commodity_sku_model = new \app\common\model\db\DbCommoditySku();

        $commodity_ids = []; // 只收集商品ID

        foreach ($sku_list as $sku_item) {
            if ($sku_item['sku_type'] == 1) {
                // 备件类型，通过sku_code查找商品
                $commodity_skus = $commodity_sku_model->where([
                    'sku_code' => ['like', '%' . $sku_item['sku_code'] . '%'],
                    'is_enable' => 1
                ])->select();

                // 收集所有匹配的商品ID
                foreach ($commodity_skus as $commodity_sku) {
                    $commodity_ids[] = $commodity_sku['commodity_id'];
                }

            } elseif ($sku_item['sku_type'] == 2) {
                // 套餐类型，通过sku_class_code对应commodity_sku.variety_code查找
                $commodity_skus = $commodity_sku_model->where([
                    'variety_code' => $sku_item['sku_class_code'],
                    'is_enable' => 1
                ])->select();

                // 收集所有匹配的商品ID
                foreach ($commodity_skus as $commodity_sku) {
                    $commodity_ids[] = $commodity_sku['commodity_id'];
                }
            }
        }

        // 去重并过滤有效的商品ID
        $commodity_ids = array_unique(array_filter($commodity_ids));

        if (empty($commodity_ids)) {
            return [];
        }

        // 验证商品是否存在且启用，只返回有效的商品ID
        $valid_commodity_ids = $commodity_model->where([
            'id' => ['in', $commodity_ids],
            'is_enable' => 1
        ])->column('id');

        return array_map('intval', $valid_commodity_ids);
    }

    /**
     * 获取卡券相关的未支付订单
     * @param string $card_id 卡券ID
     * @return array
     */
    private function getUnpaidOrdersForCard($card_id)
    {
        $order_model = new \app\common\model\bu\BuOrder();

        // 查询使用了该卡券的支付订单
        $orders = $order_model->alias('a')
            ->join('t_bu_card_receive_record b', 'a.order_code = b.consume_order_code')
            ->where([
                'b.card_id' => $card_id,
                'a.user_id' => $this->user_id,
                'a.order_status' => 2, // 已支付
                // 'b.status' => ['in', [1, 5]] // 已领取或已冻结
            ])
            ->field('a.id, a.order_code, a.order_status, a.created_date, a.total_price')
            ->order('a.created_date desc')
            ->select();

        return collection($orders)->toArray();
    }

}
