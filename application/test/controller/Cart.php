<?php
/**
 * Created by PhpStorm.
 * User: lzx
 * Date: 2020/12/22
 * Time: 3:11 PM
 */

namespace app\test\controller;


use app\common\model\act\AcByDlrCode;
use app\common\model\act\AcInsuranceSpecialist;
use app\common\model\bu\BuShoppingCart;
use app\common\model\db\DbArea;
use app\common\model\db\DbCommodityDlrType;
use app\common\model\db\DbCommoditySetSku;
use app\common\model\db\DbDlr;
use app\common\model\db\DbDlrGroup;
use app\common\model\db\DbFullDiscount;
use app\common\model\db\DbLimitDiscount;
use app\common\model\db\DbLimitDiscountCommodity;
use app\common\model\db\DbNDiscount;
use app\common\model\db\DbNDiscountInfo;
use app\common\model\db\DbSpecValue;
use app\common\net_service\Common;
use app\common\validate\Cart as CartValidate;
use hg\apidoc\annotation as Apidoc;

/**
 * 购物车
 * @Apidoc\Group("mall")
 */

class Cart extends \app\net_small\controller\Common
{


    private  $shop_cart_model;
    public function __construct()
    {


        parent::__construct();
        $this->shop_cart_model =  new BuShoppingCart();
    }

    /**
     * @Apidoc\Title("添加购物车")
     * @Apidoc\Author("lzx")
     * @Apidoc\Url("/net-small/cart/cart")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("购物车 NI+ win 0214")
     * @Apidoc\ParamType("json")
     *
     *
     * @Apidoc\Param("commodity_id",type="int(11)", require=true,desc="商品ID/主商品ID")
     * @Apidoc\Param("sku_id",type="int(11)", require=true,desc="商品规格ID:组合商品时传第一个")
     * @Apidoc\Param("shop_cart_id",type="int(11)", require=false,desc="购物车ID，传了即修改")
     * @Apidoc\Param("count",type="int(11)", require=false,desc="对应数量")
     * @Apidoc\Param("act_type_id",type="int(2)", require=false,desc="活动类型ID：1限时折扣2团购3满减4全积分折扣5套装6N件N折7预售8立减9臻享服务包10:秒杀")
     * @Apidoc\Param("act_id",type="int(11)", require=false,desc="活动类型对应ID一个商品参与一个活动")
     * @Apidoc\Param("wi_act_type_id",type="int(2)", require=false,desc="工时活动类型ID：1限时折扣3满减")
     * @Apidoc\Param("wi_act_id",type="int(11)", require=false,desc="工时活动类型对应ID一个商品参与一个活动")
     * @Apidoc\Param("mail_method",type="int(2)", require=true, desc="快递或者到店1到店2快递")
     * @Apidoc\Param("dd_dlr_code",type="string", require=false, desc="到店专营店编码")
     * @Apidoc\Param("work_time_json",type="json", require=false, desc="单商品工时json一维（sku_id:sku_id,work_time_number:工时数量，work_time_code:工时code,work_time_price:工时单价）从sku_list中获取，work_time_price或者work_time_number等于0时候不要传")
     * @Apidoc\Param("com_json",type="json", require=false, desc="子商品列表数组二维（count:数量，sku:set_skuid 跟子商品的传法一致,mail_method:快递或者到店,work_time_number:工时数量，work_time_code:工时code,work_time_price:工时单价）没有组合时候不要传")
     * @Apidoc\Param("gift_act_id",type="int(11)", require=false, desc="参与赠品活动ID，不参与可以传0"),
     * @Apidoc\Param("gift_c_json", type="array", childrenType="object", desc="赠品商品列表数组",
     *     @Apidoc\Param("commodity_id", type="string", desc="赠品商品ID"),
     *     @Apidoc\Param("set_sku_id", type="string", desc="赠品商品set_sku_id"),
     *     @Apidoc\Param("count", type="string", desc="数量"),
     *     @Apidoc\Param("mail_method", type="string", desc="送货类型，1到店/2快递"),
     *     @Apidoc\Param("dd_dlr_code", type="string", desc="到店门店编码"),
     *     @Apidoc\Param("com_json", type="array", childrenType="object", desc="赠品为组合商品，没有组合时候不要传",
     *         @Apidoc\Param("count", type="string", desc="数量"),
     *         @Apidoc\Param("sku", type="string", desc="set_skuid"),
     *         @Apidoc\Param("mail_method", type="string", desc="送货类型:快递或者到店"),
     *     ),
     * ),

     * @Apidoc\Param("hit_type_code",type="string", require=true,desc="埋点编码(ver:1020:add)")
     * @Apidoc\Param("distance",type="int", require=false,desc="公里数(双保需要传)")
     * @Apidoc\Param("vehicle_order_no",type="varchar", require=false, desc="整车订单号")
     *
     * @Apidoc\Returned("message", type="string",desc="提示消息，ok为成功")
     */
    public function addCart(CartValidate $validate){
    }

    /**
     * @Apidoc\Title("删除购物车商品")
     * @Apidoc\Author("lzx")
     * @Apidoc\Url("/net-small/cart/cart")
     * @Apidoc\Method("DELETE")
     * @Apidoc\Tag("购物车 NI+")
     * @Apidoc\Param("shop_cart_ids",type="int", require=true, desc="购物车ID，从列表获取多个使用,隔开")
     *
     * @Apidoc\Returned("message", type="string",desc="提示消息")
     */
    public function delCart(CartValidate $validate){

    }


    /**
     * @Apidoc\Title("更新购物车")
     * @Apidoc\Author("lzx")
     * @Apidoc\Url("/net-small/cart/cart")
     * @Apidoc\Method("PUT")
     * @Apidoc\Tag("购物车 NI+ 0214")
     * @Apidoc\Param("shop_cart_ids",type="int", require=true,default="0",desc="购物车ID，从列表获取多个使用,隔开")
     * @Apidoc\Param("counts",type="string", require=false,default="0",desc="数量多个使用,隔开更新数量而不是++++")
     * @Apidoc\Param("sku_ids",type="string", require=false,default="0",desc="规格ID多个用,隔开")
     * @Apidoc\Param("mail_methods",type="string", require=false,default="0",desc="快递或者到店1到店2快递多个用,隔开")
     * @Apidoc\Param("dd_dlr_code",type="string", require=false, desc="到店专营店编码，dd_commodity_type !=1,3,12 可以直接修改")
     * @Apidoc\Param("work_time_json",type="string", require=false, desc="工时，不传则判断为无工时或者不选工时")
     * @Apidoc\Param("have_work_time",type="int", require=1, desc="是否有工时 1有工时2无工时"),
     * @Apidoc\Param("use_discount",type="int(11)", require=false, desc="是否使用商品会员/车主价，1-使用，0-不使用(0423)"),
     *
     * @Apidoc\Returned("message", type="string",desc="提示信息")
     */
    public function putCart(CartValidate $validate){
//     * @Apidoc\Param("work_time_json",type="json", require=false, desc="单商品工时json（work_time_number:工时数量，work_time_code:工时code,work_time_price:工时单价）")
    }


    /**
     * @Apidoc\Title("购物车列表")
     * @Apidoc\Author("lzx")
     * @Apidoc\Url("/net-small/cart/list")
     * @Apidoc\Method("GET")
     * @Apidoc\Tag("购物车 NI+ win 0214")
     *
     * @Apidoc\Returned("list",type="array/json",desc="购物车信息列表",
     *     @Apidoc\Returned ("stock",type="int(11)",desc="库存"),
     *     @Apidoc\Returned ("sp_value_list",type="varchar(250)",desc="规格值id列表 逗号隔开"),
     *     @Apidoc\Returned ("image",type="varchar(250)",desc="规格图片"),
     *     @Apidoc\Returned ("is_sku_out",type="int(1)",desc="是否可用：0否1是"),
     *     @Apidoc\Returned ("sku_code",type="varchar(250)",desc="规格编码"),
     *     @Apidoc\Returned ("is_sold_out",type="int(1)",desc="是否出售：0否1是"),
     *     @Apidoc\Returned ("commodity_name",type="varchar(250)",desc="商品名称/主商品名称"),
     *     @Apidoc\Returned ("cover_image",type="varchar(250)",desc="规格图片"),
     *     @Apidoc\Returned ("commodity_class",type="int(1)",desc="商品种类：1-实物商品；2-虚拟商品；3-电子卡劵；4-CSS流量套餐；5-平台卡券；6-取送车券；"),
     *     @Apidoc\Returned ("commodity_card_ids",type="varchar(250)",desc="商品电子卡券id 逗号隔开"),
     *     @Apidoc\Returned ("card_id",type="varchar(250)",desc="卡劵号"),
     *     @Apidoc\Returned ("commodity_id",type="int(11)",desc="商品id"),
     *     @Apidoc\Returned ("is_store",type="int(1)",desc="是否门店自提（1是，0否）默认是"),
     *     @Apidoc\Returned ("favourable_introduction",type="varchar(250)",desc="优惠简称"),
     *     @Apidoc\Returned ("favourable_detail",type="varchar(500)",desc="优惠详情"),
     *     @Apidoc\Returned ("dlr_pay_bank_type",type="varchar(500)",desc="到店支付方式01,02;如果有分组0102共有组分到第一组"),
     *     @Apidoc\Returned ("pay_style",type="tinyint(3)",desc="1现金+积分 2现金 3积分"),
     *     @Apidoc\Returned ("b_act_price",type="decimal(10,2)",desc="价格"),
     *     @Apidoc\Returned ("price",type="decimal(10,2)",desc="价格"),
     *     @Apidoc\Returned ("have_work_price",type="tinyint(3)",desc="是否有工时费1有，0无"),
     *     @Apidoc\Returned ("act_status", type="int(11)", desc="1未开始，2进行中，3已结束"),
     *     @Apidoc\Returned ("dd_commodity_type", type="int(11)", desc="1保养套餐-老友惠保养套餐,3保养套餐-心悦保养套餐,4保养套餐-五年双保升级权益套餐"),
     *     @Apidoc\Returned("limit_dis", type="json", desc="限时优惠json活动信息",
     *         @Apidoc\Returned("key为专营店编号", type="array", desc="对应值为限时折扣id" )
     *     ),
     *     @Apidoc\Returned("n_dis", type="json", desc="N件N折json活动信息",
     *         @Apidoc\Returned("key为专营店编号", type="array", desc="对应值为N件N折id（数组）" )
     *     ),
     *     @Apidoc\Returned("group_dis", type="json", desc="团购json活动信息",
     *         @Apidoc\Returned("key为专营店编号", type="array", desc="对应值为团购id（数组）" )
     *     ),
     *     @Apidoc\Returned("pre_dis", type="json", desc="预售json活动信息",
     *         @Apidoc\Returned("key为专营店编号", type="array", desc="对应值为预售id（数组）" )
     *     ),
     *     @Apidoc\Returned("cheap_dis", type="json", desc="套餐json活动信息",
     *         @Apidoc\Returned("key为专营店编号", type="array", desc="对应值为套餐id（数组）" )
     *     ),
     *     @Apidoc\Returned("full_dis", type="json", desc="满减json活动信息",
     *         @Apidoc\Returned("key为专营店编号", type="array", desc="对应值为满减id（数组）" )
     *     ),
     *     @Apidoc\Returned("seckill_dis", type="json", desc="秒杀json活动信息",
     *         @Apidoc\Returned("key为专营店编号", type="array", desc="对应值秒杀id（数组）" )
     *     ),
     *     @Apidoc\Returned("seckill_info", type="json", desc="秒杀json活动信息",
     *         @Apidoc\Returned("key为专营店编号", type="array", desc="对应值秒杀id（数组）" ),
     *         @Apidoc\Returned("act_status", type="int(11)", desc="1未开始，2进行中，3已结束"),
     *     ),
     *     @Apidoc\Returned ("commodity_dlr_type_id",type="int(11)",desc="商品类型id,关联  commodity_dlr_type表"),
     *     @Apidoc\Returned ("is_card_multic",type="int(1)",desc="优惠券是否可领取（1是，0否）"),
     *     @Apidoc\Returned ("mail_price",type="decimal(10,2)",desc="快递费"),
     *     @Apidoc\Returned ("divided_into",type="decimal(10,2)",desc="分成比例"),
     *     @Apidoc\Returned ("install_fee",type="decimal(10,2)",desc="安装费"),
     *     @Apidoc\Returned ("commodity_set_id",type="int(11)",desc="商品上架id"),
     *     @Apidoc\Returned ("max_point",type="int(11)",desc="最多使用积分"),
     *     @Apidoc\Returned ("factory_points",type="int(1)",desc="是否支持厂家积分(1是，0否）"),
     *     @Apidoc\Returned ("dlr_points",type="int(1)",desc="是否支持专营店积分（1是0否）"),
     *     @Apidoc\Returned ("act_json",type="array/json",desc="保存活动对应idjson 活动1=>''限时折扣'',2=>''团购'',3=>''满减'',4=>''全积分折扣'',5=>''套装'',6=>''N件N折'',7=>''预售'',''8''=>''立减''"),
     *     @Apidoc\Returned ("cart_id",type="int(11)",desc="购物车表id"),
     *     @Apidoc\Returned ("count",type="int(11)",desc="数量"),
     *     @Apidoc\Returned ("sku_id",type="int(11)",desc="选择的SKU_ID"),
     *     @Apidoc\Returned ("order_mail_type",type="int(2)",desc="订单商品快递或者到店1快递2到店支付到不同账号"),
     *     @Apidoc\Returned ("mail_method",type="int(2)",desc="订单商品快递或者到店1快递2到店支付到不同账号"),
     *     @Apidoc\Returned ("commodity_dlr_type",type="int(11)",desc="商品专营店分类ID"),
     *     @Apidoc\Returned ("work_time_number",type="varchar(50)",desc="工时数量"),
     *     @Apidoc\Returned ("work_time_price",type="varchar(50)",desc="工时单价需要数量*工时数量*购物车数量才是总商品工时"),
     *     @Apidoc\Returned ("act",type="array/json",desc="满优惠活动|限时折扣活动json",
     *          @Apidoc\Returned ("act_code",type="int(11)",desc="活动code"),
     *          @Apidoc\Returned ("act_id",type="int(11)",desc="活动id"),
     *          @Apidoc\Returned ("act_name",type="varchar(200)",desc="活动名"),
     *          @Apidoc\Returned ("can_buy_number", type="varchar(200)", desc="满减活动，可参与次数，大于0表示可参与活动" ),
     *          @Apidoc\Returned ("act_status", type="int(11)", desc="1未开始，2进行中，3已结束"),
     *          @Apidoc\Returned ("commodity_dis_act_user_segment",type="string",desc="活动折扣：0-无/1-会员价/2-车主价(0423)"),
     *          @Apidoc\Returned ("commodity_dis_label",type="string",desc="会员/车主价标签(0423)"),
     *          @Apidoc\Returned("activity_image", type="string", desc="活动头图" ),
     *     ),
     *     @Apidoc\Returned ("sp_name",type="varchar(250)",desc="商品+规格名"),
     *     @Apidoc\Returned ("sku_image",type="varchar(250)",desc="商品规格图片"),
     *     @Apidoc\Returned ("can_number",type="int(11)",desc="可购买数量"),
     *
     *     @Apidoc\Returned ("use_discount",type="string",desc="是否使用商品会员/车主价，1-使用，0-不使用(0423)"),
     *     @Apidoc\Returned ("commodity_dis_user_segment",type="string",desc="商品折扣：0-无/1-会员价/2-车主价(0423)"),
     *     @Apidoc\Returned ("commodity_dis_act_user_segment",type="string",desc="活动折扣：0-无/1-会员价/2-车主价(0423)"),
     *     @Apidoc\Returned ("commodity_dis_label",type="string",desc="会员/车主价标签(0423)"),
     *     @Apidoc\Returned ("commodity_dis_act_segment_price",type="string",desc="商品折扣后活动折扣价(0423)"),
     *     @Apidoc\Returned ("commodity_dis_act_original_price",type="string",desc="商品原价活动折扣价(0423)"),
     * ),
     * @Apidoc\Returned("full_id", type="int(10)", desc="满减ID有满减时显示" ),
     * @Apidoc\Returned("all_full_money", type="decimal(11,2)", desc="满减金额(实际以actchange接口请求为准)" ),
     * @Apidoc\Returned ("all_full_info",type="array/json",desc="满优惠json",
     *      @Apidoc\Returned ("mj",type="decimal(11,2)",desc="当前档减多少"),
     *      @Apidoc\Returned ("mj_ed",type="decimal(11,2)",desc="当前档"),
     *      @Apidoc\Returned ("next_mj",type="decimal(11,2)",desc="下一档减多少"),
     *      @Apidoc\Returned ("next_mj_ed",type="decimal(11,2)",desc="下一档门槛"),
     *      @Apidoc\Returned ("next_mj_ed_cha",type="decimal(11,2)",desc="下一档门槛差多少"),
     *      @Apidoc\Returned ("full_word",type="varchar(500)",desc="满减标题如购满20元，可减2元，还差19.8元")
     * ),
     * @Apidoc\Returned("n_dis_id", type="int(10)", desc="N件ID有满减时显示" ),
     * @Apidoc\Returned("all_dis_count", type="int(10)", desc="N件(实际以actchange接口请求为准)" ),
     * @Apidoc\Returned ("all_dis_info",type="array/json",desc="N件优惠json",
     *      @Apidoc\Returned ("dis_p",type="decimal(11,2)",desc="N件折扣"),
     *      @Apidoc\Returned ("dis_ed",type="decimal(11,2)",desc="N件当前档"),
     *      @Apidoc\Returned ("next_dis",type="decimal(11,2)",desc="下一档折扣多少"),
     *      @Apidoc\Returned ("next_dis_ed",type="decimal(11,2)",desc="下一档门槛"),
     *      @Apidoc\Returned ("dis_info_desc",type="varchar(500)",desc="N件N折优惠描述")
     * ),
     * @Apidoc\Returned("work_time_price", type="int(10)", desc="工时单价" ),
     * @Apidoc\Returned ("sub_sku",type="array/json",desc="子商品信息",
     *      @Apidoc\Returned ("title",type="decimal(11,2)",desc="子商品商品名"),
     *      @Apidoc\Returned ("count",type="int(10)",desc="子商品数量"),
     *      @Apidoc\Returned ("work_time_number",type="varchar(50)",desc="工时数量"),
     *      @Apidoc\Returned ("work_time_code",type="varchar(50)",desc="工时编码"),
     *      @Apidoc\Returned ("work_time_price",type="varchar(500)",desc="工时单价需要数量*工时数量*购物车数量才是总商品工时"),
     *      @Apidoc\Returned ("sku_cn",type="array/json",desc="规格"),
     *     @Apidoc\Returned ("count_stock",type="int(10)",desc="子商品的总库存")
     * ),
     * @Apidoc\Returned("gift_act_id", type="string", desc="赠品活动ID，不参与时为0"),
     * @Apidoc\Returned("gift_c_json_cn", type="array", childrenType="object", desc="赠品商品列表",
     *     @Apidoc\Returned("commodity_id", type="string", desc="赠品商品ID"),
     *     @Apidoc\Returned("set_sku_id", type="string", desc="赠品商品 set_sku_id"),
     *     @Apidoc\Returned("count", type="string", desc="赠品商品数量"),
     *     @Apidoc\Returned("mail_method", type="string", desc="赠品商品送货类型"),
     *     @Apidoc\Returned("dd_dlr_code", type="string", desc="赠品商品到店编码"),
     *     @Apidoc\Returned("title", type="string", desc="赠品商品名称"),
     *     @Apidoc\Returned("sku_cn", type="string", desc="赠品商品规格名称"),
     *     @Apidoc\Returned("price", type="string", desc="赠品商品划线价"),
     *     @Apidoc\Returned("sp_value_list", type="string", desc="子商品规格ID"),
     *     @Apidoc\Returned("sp_name", type="string", desc="子商品规格名称"),
     *     @Apidoc\Returned("cover_image", type="string", desc="商品头图"),
     *     @Apidoc\Returned("car_18n", type="string", desc="加购18位码"),
     *     @Apidoc\Returned("cart_car_info", type="string", desc="加购车型信息"),
     *     @Apidoc\Returned("dd_dlr_name", type="string", desc="到店名称"),
     *     @Apidoc\Returned("stock", type="int", desc="库存数量"),
     *     @Apidoc\Returned("is_enable", type="int", desc="在售状态，1在售，0下架"),
     *     @Apidoc\Returned("com_json", type="array", childrenType="object", desc="组合商品，子商品列表",
     *         @Apidoc\Returned("count", type="string", desc="子商品数量"),
     *         @Apidoc\Returned("sku", type="string", desc="子商品set_sku_id"),
     *         @Apidoc\Returned("title", type="string", desc="子商品名称"),
     *         @Apidoc\Returned("sku_cn", type="string", desc="子商品规格"),
     *         @Apidoc\Returned("sp_value_list", type="string", desc="子商品规格ID"),
     *         @Apidoc\Returned("sp_name", type="string", desc="子商品规格名称"),
     *         @Apidoc\Returned("stock", type="int", desc="库存数量"),
     *         @Apidoc\Returned("is_enable", type="int", desc="在售状态，1在售，0下架"),
     *     ),
     * ),
     * @Apidoc\Returned("gift_act", type="object", desc="赠品活动信息",
     *     @Apidoc\Returned("stop_time", type="string", desc="活动截止时间"),
     *     @Apidoc\Returned("can_no", type="string", desc="可参与次数"),
     *     @Apidoc\Returned("gift_info", type="array/json", desc="买赠信息",
     *      @Apidoc\Returned("id", type="int(11)", desc="买赠活动id" ),
     *      @Apidoc\Returned("title", type="varchar(20)", desc="活动名称" ),
     *      @Apidoc\Returned("des", type="text", desc="活动规则" ),
     *      @Apidoc\Returned("start_time", type="datetime", desc="开始时间" ),
     *      @Apidoc\Returned("end_time", type="datetime", desc="结束时间" ),
     *      @Apidoc\Returned("purchase_number", type="int(11)", desc="剩余次数" ),
     *      @Apidoc\Returned("optional_number", type="int(11)", desc="可选择N件" ),
     *      @Apidoc\Returned("act_status", type="tinyint(1)", desc="1未开始，2进行中，3已结束" ),
     *      @Apidoc\Returned ("is_participate",type="tinyint(1)", desc="是否参加 1参加 0未参加"),
     *      @Apidoc\Returned ("gift_imgs_arr", type="array/json", desc="买赠主图",
     *              @Apidoc\Returned("is_choose", type="tinyint(1)", desc="是否可选 1可选 0不可选" ),
     *              @Apidoc\Returned("cover_image", type="varchar(60)", desc="赠品主图" )
     *      ),
     *      @Apidoc\Returned("activity_image", type="string", desc="活动头图" ),
     *     ),
     * ),
     * @Apidoc\Returned("maintain_dis", type="varchar", desc="保养套餐折扣 比如：7.5" ),
     * @Apidoc\Returned("can_use_card", type="tinyint(1)", desc="可用券类型，1可用券，2其他规格可用，0无券" ),
     */
    public function CartList(CartValidate $validate){

    }


    /**
     * @Apidoc\Title("购物车活动重选优惠")
     * @Apidoc\Author("lzx")
     * @Apidoc\Url("/net-small/cart/act-change")
     * @Apidoc\Method("GET")
     * @Apidoc\Tag("购物车 NI+")
     *
     * @Apidoc\Param("shop_cart_ids", type="string(255)",require=true,desc="购物车ID" )
     * @Apidoc\Param("act_type_id", type="string(255)",require=true,desc="活动类型ID：3满减，6N件N折" )
     * @Apidoc\Param("act_id", type="string(255)",require=true,default="1",desc="活动ID" )

     *     @Apidoc\Returned ("full_word",type="string",desc="满减词"),
     *     @Apidoc\Returned ("full_yh",type="string",desc="满减优惠"),
     *     @Apidoc\Returned ("n_dis_money",type="string",desc="N件优惠金额"),
     *     @Apidoc\Returned ("n_dis_word",type="string",desc="N件词"),
     *     @Apidoc\Returned ("n_dis",type="string",desc="N件折扣"),
     *
     */
    public function actChange(CartValidate $validate){


    }

    /**
     * @Apidoc\Title("选择专营店")
     * @Apidoc\Author("lzx")
     * @Apidoc\Url("/net-small/cart/dlr")
     * @Apidoc\Method("GET")
     * @Apidoc\Tag("购物车 专营店 NI+")
     * @Apidoc\Param("commodity_id", type="int(10)",require=true,default="0",desc="商品ID" )
     * @Apidoc\Param("area_id", type="int(10)",require=false,default="0",desc="地区ID,不传可得地区列表," )
     * @Apidoc\Param("group_id", type="int(10)",require=false,default="0",desc="专营店分组ID运维提供" )
     * @Apidoc\Param("order", type="string(255)",require=true,default="score",desc="排序方式：score为综合评分排序，distance为距离排序" )
     * @Apidoc\Param("longitude", type="string(255)",require=false,default="",desc="经度，不传则不计算距离" )
     * @Apidoc\Param("latitude", type="string(255)",require=false,default="",desc="经度，不传则不计算距离" )
     * @Apidoc\Param("ly_bank_code", type="string(50)",require=false,default="",desc="开户行编码" )
     * @Apidoc\Param("dlr_name", type="string(255)",require=false,default="",desc="经销商名称" )
     * @Apidoc\Param("goods_ids", type="string(255)",require=false,default="",desc="商品id,多个用,隔开" )
     *
     * @Apidoc\Returned ("id",type="string",desc="专营店id"),
     * @Apidoc\Returned ("dlr_name",type="string",desc="专营店名称"),
     * @Apidoc\Returned ("dlr_code",type="string",desc="专营店编码"),
     * @Apidoc\Returned ("base_sale_tel",type="string",desc="销售电话"),
     * @Apidoc\Returned ("can_user_point",type="string",desc="能否使用积分1可以2不行"),
     * @Apidoc\Returned ("base_service_tel",type="string",desc="服务电话"),
     * @Apidoc\Returned ("base_address",type="string",desc="地址"),
     * @Apidoc\Returned ("base_dlr_type",type="string",desc="网店类型：1一网店，2二网店"),
     * @Apidoc\Returned ("base_comprehensive_score",type="string",desc="综合评分"),
     * @Apidoc\Returned ("base_service_score",type="string",desc="服务评分"),
     * @Apidoc\Returned ("base_product_score",type="string",desc="商品评分"),
     * @Apidoc\Returned ("base_is_drive",type="string",desc="是否试驾"),
     * @Apidoc\Returned ("base_is_ssa_fast",type="string",desc="是否开通快速保养"),
     * @Apidoc\Returned ("base_is_ssa_self",type="string",desc="是否开通自助保养"),
     * @Apidoc\Returned ("base_is_ssa_spray",type="string",desc="是否开通极速钣喷"),
     * @Apidoc\Returned ("base_is_take_send",type="string",desc="是否取送车"),
     * @Apidoc\Returned ("base_is_friday",type="string",desc="是否星光星期五"),
     * @Apidoc\Returned ("base_sales_score",type="decimal(4,1)",desc="销售心级评分"),
     * @Apidoc\Returned ("base_after_sales_score",type="decimal(4,1)",desc="售后心级评分"),
     * @Apidoc\Returned ("base_csi_score",type="decimal(6,3)",desc="CSI综合考评得分"),
     * @Apidoc\Returned ("base_ssi_score",type="decimal(6,3)",desc="SSI综合考评得分"),
     * @Apidoc\Returned ("base_star_score",type="decimal(6,3)",desc="心级评价综合得分"),
     * @Apidoc\Returned ("distance",type="string",desc="距离"),
     * @Apidoc\Returned ("base_business_hours_begin",type="string",desc="营业开始"),
     * @Apidoc\Returned ("base_business_hours_end",type="string",desc="营业结束时间"),
     * @Apidoc\Returned ("afs_service_star",type="string",desc="服务星级"),
     *
     */
    public function choose_dlr(CartValidate $validate){

    }

    //公用版选择专营店
    public function cho_dlr($area_id='', $group_id=15, $params = [])
    {

    }
    //保养套餐选择专营店
    public function cho_dlr_by($city='',$group_id=''){

    }

    /**
     * @Apidoc\Title("地区列表")
     * @Apidoc\Author("lzx")
     * @Apidoc\Url("/net-small/cart/area")
     * @Apidoc\Method("GET")
     * @Apidoc\Tag("购物车 地区 NI+")
     * @Apidoc\Returned("",type="array",desc="",
     *     @Apidoc\Returned ("name",type="string",desc="地区名"),
     *     @Apidoc\Returned ("code",type="int",desc="地区编号"),
     *     @Apidoc\Returned("city",type="array",desc="城市",
     *          @Apidoc\Returned ("name",type="string",desc="地区名"),
     *          @Apidoc\Returned ("code",type="int",desc="地区编号"),
     *              @Apidoc\Returned("area",type="array",desc="城市",
     *                     @Apidoc\Returned ("name",type="string",desc="区编名"),
     *                     @Apidoc\Returned ("code",type="int",desc="区编号"),
     *              )
     *      )
     *  )
     */
    public function area(){

    }
    /**
     * @Apidoc\Title("购物车显示卡券+金额总数")
     * @Apidoc\Author("lzx")
     * @Apidoc\Url("/net-small/cart/point-money")
     * @Apidoc\Method("GET")
     * @Apidoc\Tag("购物车 PZ1A")
     * @Apidoc\Param("shop_cart_ids",type="varchar(50)", require=true,desc="购物车ID多个用,隔开")
     * @Apidoc\Returned("all_point", type="string",desc="需付积分数"),
     * @Apidoc\Returned("all_money", type="string",desc="需付现金"),
     * @Apidoc\Returned("limit_dis", type="string",desc="限时优惠了多少"),
     * @Apidoc\Returned("full_dis", type="string",desc="满减优惠了多少"),
     * @Apidoc\Returned("n_dis", type="string",desc="N件折扣优惠了多少"),
     * @Apidoc\Returned("seckill_dis", type="string",desc="秒杀优惠了多少"),
     * @Apidoc\Returned("gift_act_dis", type="string",desc="赠品优惠了多少"),
     * @Apidoc\Returned("cart_goods_18_n_count", type="int",desc="选择的购物车有多少个18位码"),
     * @Apidoc\Returned("all_maintain_dis", type="string",desc="保养套餐优惠"),
     * @Apidoc\Returned("all_segment_membership_dis", type="decimal(11,2)", desc="会员优惠" ),
     * @Apidoc\Returned("all_segment_owner_dis", type="decimal(11,2)", desc="车主优惠" ),
     * @Apidoc\Returned("all_dd_pay_model", type="array/json",desc="每个购物车ID对应支付行",
     *     @Apidoc\Returned ("cart_id",type="int",desc="购物车ID"),
     *     @Apidoc\Returned ("ly_bank_code",type="string",desc="银行编码，需要根据编码进行分组下单 01、02"),
     *)
     *
     */
    public function cart_point_money(){
    }

    /**
     * @Apidoc\Title("购物车数量")
     * @Apidoc\Author("llj")
     * @Apidoc\Url("/net-small/cart/cart-count")
     * @Apidoc\Method("GET")
     * @Apidoc\Tag("购物车 PZ1A NI+ win")
     *
     * @Apidoc\Returned("cart_count", type="int",desc="购物车数量"),
     *
     */
    public function cart_count(){
    }

    /**
     * @Apidoc\Title("购物车赠品券数量校验")
     * @Apidoc\Author("lzx")
     * @Apidoc\Url("/net-small/cart/check-gift-card-count")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("购物车 NI+ 赠品券校验")
     *
     * @Apidoc\Param("cart_ids",type="varchar(50)", require=true,desc="购物车ID多个用,隔开")
     *
     * @Apidoc\Returned("can_checkout", type="int", desc="是否可以下单")
     * @Apidoc\Returned("message", type="string", desc="提示信息")
     * @Apidoc\Returned("validation_results", type="array", desc="校验结果详情")
     * @Apidoc\Returned("cart_id", type="int", desc="购物车ID")
     * @Apidoc\Returned("commodity_id", type="int", desc="商品ID")
     * @Apidoc\Returned("can_select", type="bool", desc="是否可以选择")
     * @Apidoc\Returned("message", type="string", desc="校验信息")
     * @Apidoc\Returned("gift_card_count", type="int", desc="待激活赠品券数量")
     * @Apidoc\Returned("selected_gift_count", type="int", desc="已选择赠品数量")
     * @Apidoc\Returned("is_main_product", type="bool", desc="是否为主品")
     */
    public function checkGiftCardCount()
    {}

}
