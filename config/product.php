<?php
/**
 * 开发环境
 * @author: xie<PERSON><PERSON><PERSON>
 * @date: 2017-03-10
 */

use think\Env;

date_default_timezone_set('PRC');
return [

    //日志记录路径
    'logger_path' => LOG_PATH,

    'appid'            => 'wx8f7cf7f1d1059c77',
    'appsecret'        => '7c46af7b20c4b25ff4dd93d7969b7ab5',
    'xn_siteid'        => 'kf_9255',
    //-----------------API---------------------------
    // 应用调试模式
    'app_debug'        => false,
    // 应用Trace
    'app_trace'        => false,
    'show_error_msg'   => false,
    // +----------------------------------------------------------------------
    // | Trace设置 开启 app_trace 后 有效
    // +----------------------------------------------------------------------
    'trace'            => [
        // 内置Html Console 支持扩展
        'type' => 'Console',
    ],
    // URL普通方式参数 用于自动生成
    'url_common_param' => true,
    // 默认全局过滤方法 用逗号分隔多个
    //    'default_filter'         => 'htmlspecialchars',
    // +----------------------------------------------------------------------
    // | 缓存设置
    // +----------------------------------------------------------------------
    // 视图输出字符串内容替换
    'view_replace_str' => [  //TODO 根据环境自定义
                             '__PUBLIC__' => '/public/',
                             '__STATIC__' => '/public/static/',
    ],

    //-----------附件上传目录------
    'upload'           => [
        'path'       => APP_PATH . '../public/uploads/',
        'url'        => '',//
        'qrc_url'    => 'http://wxstore.chebaba.com/public/uploads/',//TODO 生产环境
        'image_size' => 209715200,     //图片大小限制 默认200M 视频
        'image_ext'  => ['gif', 'jpg', 'jpeg', 'bmp', 'png', 'avi', 'mp4', 'pdf'], //图片格式限制
        'file_size'  => 15728640,    //文件大小限制10m
        'video_ext'  => ['avi', 'wmv', 'mpeg', 'mp4', 'mov', 'mkv', 'flv', 'f4v', 'm4v', 'rmvb', 'rm', '3gp'],
    ],

    'cache'         => [
        // 驱动方式
        'type'     => Env::get('type', 'redis'),
        //服务器地址
        'host'     => Env::get('cache_host', 'r-wz96f04ed5fc0194.redis.rds.aliyuncs.com'), //TODO 开发环境
        'port'     => Env::get('cache_port', 6379),
        'password' => Env::get('cache_password', 'aLHdlnMzQgq6Av18'),
        'expire'   => 0,
        'prefix'   => Env::get('cache_prefix', 'think'),
    ],

    //---------------DATABASE 数据库------------------------
    //数据库配置
    'database'      => [
        // 数据库类型
        'type'        => 'mysql',
        // 服务器地址
        'hostname'    => Env::get('env_database_hostname', 'rm-wz90ljx21j40qd773.mysql.rds.aliyuncs.com'),
        // 数据库名
        'database'    => Env::get('env_database_database', 'dealer'),
        // 用户名
        'username'    => Env::get('env_database_username', 'dealeruser'),
        // 密码
        'password'    => Env::get('env_database_password', 'cPNNrY@%80jP0fer'),
        // 端口
        'hostport'    => '3306',
        // 数据库连接参数
        'params'      => [
            PDO::ATTR_CASE => PDO::CASE_LOWER //字段不区分大小写
        ],
        // 数据库编码默认采用utf8
        'charset'     => 'utf8',
        // 数据库表前缀
        'prefix'      => 't_',
        // 数据库调试模式
        'debug'       => Env::get('DEV_DB_DEBUG', false),
        // 是否需要进行SQL性能分析
        'sql_explain' => false,
    ],
    //数据库配置
    'database-prod' => [
        // 数据库类型
        'type'        => 'mysql',
        // 服务器地址
        'hostname'    => Env::get('env_database_hostname_prod', 'rm-wz9xz46u8yk73ti89o.mysql.rds.aliyuncs.com'),
        // 数据库名
        'database'    => Env::get('env_database_database_prod', 'dealer'),
        // 用户名
        'username'    => Env::get('env_database_username_prod', 'dealeruser'),
        // 密码
        'password'    => Env::get('env_database_password_prod', 'cPNNrY@%80jP0fer'),
        // 端口
        'hostport'    => '3306',
        // 数据库连接参数
        'params'      => [
            PDO::ATTR_CASE => PDO::CASE_LOWER //字段不区分大小写
        ],
        // 数据库编码默认采用utf8
        'charset'     => 'utf8',
        // 数据库表前缀
        'prefix'      => 't_',
        // 数据库调试模式
        'debug'       => true,
        // 是否需要进行SQL性能分析
        'sql_explain' => false,
    ],
    'database-read' => [
        // 数据库类型
        'type'        => 'mysql',
        // 服务器地址
        'hostname'    => Env::get('env_database_hostname_read', 'rm-wz90ljx21j40qd773.mysql.rds.aliyuncs.com'),
        // 数据库名
        'database'    => Env::get('env_database_database_read', 'dealer'),
        // 用户名
        'username'    => Env::get('env_database_username_read', 'dealeruser'),
        // 密码
        'password'    => Env::get('env_database_password_read', 'cPNNrY@%80jP0fer'),
        // 端口
        'hostport'    => '3306',
        // 数据库连接参数
        'params'      => [
            PDO::ATTR_CASE => PDO::CASE_LOWER //字段不区分大小写
        ],
        // 数据库编码默认采用utf8
        'charset'     => 'utf8',
        // 数据库表前缀
        'prefix'      => 't_',
        // 数据库调试模式
        'debug'       => true,
        // 是否需要进行SQL性能分析
        'sql_explain' => false,
    ],

    // +----------------------------------------------------------------------
    // | runtime日志设置
    // +----------------------------------------------------------------------
    'log'           => [
        // 日志记录方式，内置 file socket 支持扩展
        'type'      => 'File',
        // 日志保存目录
        'path'      => LOG_PATH,
        // 日志记录级别
        'level'     => ['log', 'notice', 'error'],
        'file_size' => 209715200,
    ],

    'session'             => [
        'id'             => '',
        // SESSION_ID的提交变量,解决flash上传跨域
        'var_session_id' => '',
        // SESSION 前缀
        'prefix'         => 'sess_product',
        // 驱动方式 支持redis memcache memcached
        'type'           => Env::get('session_type', ''),
        // 是否自动开启 SESSION
        'auto_start'     => true,
        // redis主机
        'host'           => Env::get('cache_host', 'r-wz96f04ed5fc0194.redis.rds.aliyuncs.com'),
        // redis端口
        'port'           => Env::get('cache_port', 6379),
        // 密码
        'password'       => Env::get('cache_password', 'aLHdlnMzQgq6Av18'),
        'secure'         => Env::get('cache_secure', true),
        'httponly'       => Env::get('cache_httponly', true),
        'persistent'     => false,
    ],
    /*'cache' =>  [
        // 文件缓存
        // 使用复合缓存类型
        'type'  =>  'complex',
        'file'   =>  [
            // 驱动方式
            'type'   => 'file',
            // 设置不同的缓存保存目录
            'path'   => RUNTIME_PATH . 'file/',
        ],
        // redis缓存
        'redis'   =>  [
            // 驱动方式
            'type'   => 'redis',
            // 服务器地址
            'host'       => '127.0.0.1',
            //前缀
            'prefix'=>  ‘product’
        ],
    ],*/
    'PAY_CHEBABA'         => 1,
    'NISSAN_API_TOKEN'    => '4s_STORE_api',
    'NISSAN_URL'          => 'https://weixin.dongfeng-nissan.com.cn/', //todo 测试用开发环境
    //    'NISSAN_URL'=>'http://dfldata-dev.dongfeng-nissan.com.cn/NISSAN_TEST/Nissan/www/',
    'component_appid'     => 'wx88adbb44fc787a0f',
    'component_appsecret' => 'b9d0494e33154715348381c50b775d3d',
    'component_token'     => 'NissanAfterSale',
    'component_aeskey'    => 'xAybX1tcgoPC9f0YHhJxD5ojmexWMp2r9iZaO0CDeoB',
    //企业号url
    'mhome'               => [
        'base_url' => 'http://qiye.chebaba.com',
    ],
    //首页读取的三个车系ID
    'by_id'               => 4,//爱车保养
    'bj_id'               => 7,//售后备件
    'pq_id'               => 6,//钣金喷漆

    'jd_fuli' => [
        'client_id'     => 'ZvWuvdAI4NDOfA3Z41sd',
        'client_secret' => 'WpBG16k6ssZni8DobL6r',
        'username'      => '东风数据正式帐号',
        'password'      => 'jd123456',
        'email'         => '<EMAIL>',
        'del_msg'       => true,  //生产环境删消息
        'jdimg_url'     => 'https://img13.360buyimg.com/n1/s800x800_',

    ],

    'wx_app'           => [
        'appid'      => 'wx8f4ba17d7b77a1df',
        'app_secret' => 'f21a69c7e8a2749e19ddb9648e13770b',
    ],

    //积分商城小程序
    'jifen_app'        => [
        'appid'       => 'wxac2f82a4cb0029ca',
        'app_secret'  => '3fa2c026ec13c2f4eaa2381570c2179d',
        'no_class_id' => [
            6756, 11849, 11850, 13295, 13992, 14880, 16874, 16875, 12216, 17038, 6980, 15054, 1502, 1503, 1504, 1505, 1506, 12609, 12610, 12611, 14697, 14698, 14699, 14700, 14701, 1368, 1502, 1503, 1504, 1505, 1506, 1508, 1509, 1510, 2687, 5024, 6756, 6980, 11849, 11850, 11852, 12079, 12187, 12188, 12189, 12216, 12240, 12241, 12242, 12243, 12244, 12245, 12587, 12588, 12590, 12591, 12593, 12594, 12595, 12596, 12597, 12609, 12610, 12611, 13554, 13555, 13556, 13558, 13560, 13561, 13562, 13564, 13565, 13566, 13567, 13568, 13569, 13571, 13572, 13575, 13578, 13579, 13580, 13582, 13583, 13587, 13588, 13590, 13595, 13596, 13601, 13603, 13892, 13992, 14053, 14103, 14223, 14697, 14698, 14699, 14700, 14701, 14880, 14890, 15054, 15565, 15566, 15618, 15619, 15620, 17038, 17303, 18207, 19467, 1508, 1509, 1510, 2687, 12187, 12188, 12189, 12587, 12588, 12590, 12591, 12593, 12594, 12595, 12596, 12597, 13892, 14223, 12603, 1559, 12640, 12079, 9921, 6796, 967, 1291, 12113, 6155, 6160, 6167, 6172, 6174, 6182, 12040, 12041, 12042, 13062, 13915, 19933
        ],//排除上线的商品分类
        'up_class_id' => "655,672,673,674,675,678,679,680,681,682,683,684,687,688,689,690,691,692,693,694,695,698,700,701,702,717,718,719,721,722,723,724,725,727,728,739,740,741,742,745,747,748,749,750,751,753,754,755,756,757,758,759,760,761,762,795,798,803,806,825,826,862,863,866,867,868,870,878,880,881,882,897,898,899,901,902,963,967,983,1009,1047,1048,1049,1050,1051,1098,1099,1105,1278,1279,1283,1287,1289,1291,1300,1301,1389,1390,1391,1392,1396,1420,1421,1422,1423,1424,1425,1426,1428,1449,1472,1473,1474,1475,1476,1477,1478,1479,1480,1483,1484,1533,1534,1537,1538,1539,1546,1548,1550,1551,1552,1553,1555,1556,1557,1558,1559,1560,1562,1563,1564,1565,1566,1567,1568,1569,1589,1590,1591,1592,1593,1594,1595,1601,1602,1656,1657,1658,1660,1661,1694,1695,1696,1697,1698,1699,1700,1701,2601,2603,2629,2631,2644,2656,2675,2676,2677,2678,2679,2680,2691,2694,3977,3986,4693,4702,4837,4839,4840,4935,4937,4998,4999,5000,5001,5002,5008,5009,5010,5020,5021,5022,5023,5024,5146,5152,5153,5155,5156,6199,6200,6201,6202,6203,6204,6205,6206,6207,6209,6210,6211,6212,6215,6216,6217,6218,6220,6221,6222,6223,6224,6225,6228,6230,6231,6232,6315,6316,6317,6319,6749,6752,6753,6757,6766,6767,6768,6769,6785,6795,6796,6798,6801,6804,6807,6850,6964,6965,6972,7052,7054,7055,7057,7058,7060,7061,7062,7371,7372,7373,7374,7375,9183,9184,9185,9248,9249,9399,9434,9754,9755,9756,9757,9758,9759,9761,9762,9763,9764,9765,9766,9767,9768,9959,9961,9962,9964,9971,9974,9985,10969,10974,10975,11148,11149,11150,11151,11152,11153,11154,11155,11162,11163,11164,11166,11167,11222,11223,11224,11225,11226,11227,11302,11303,11304,11762,11843,11852,11859,11867,11875,11878,11879,11880,11881,11882,11883,11886,11887,11888,11889,11898,11932,11951,11953,11954,11955,11964,11965,11966,11967,11968,11969,11975,11976,11977,11978,11979,11980,11981,12100,12101,12103,12104,12107,12108,12109,12110,12111,12112,12116,12117,12118,12119,12120,12121,12123,12124,12126,12127,12128,12129,12130,12131,12132,12133,12134,12135,12136,12137,12138,12139,12140,12142,12143,12145,12146,12148,12149,12150,12151,12152,12153,12155,12156,12157,12158,12159,12160,12161,12191,12192,12194,12195,12196,12197,12198,12199,12200,12201,12203,12204,12205,12206,12207,12208,12209,12210,12211,12212,12213,12215,12217,12341,12370,12376,12392,12394,12395,12397,12398,12400,12401,12406,12407,12408,12409,12429,12798,12799,12801,12802,12803,12804,12805,12809,12811,12858,12859,12861,13116,13117,13118,13243,13244,13245,13247,13248,13249,13250,13251,13252,13253,13254,13255,13269,13286,13287,13288,13289,13290,13291,13292,13293,13296,13298,13544,13546,13547,13548,13549,13657,13658,13659,13660,13661,13690,13691,13701,13702,13757,13760,13781,13782,13783,13784,13785,13789,13790,13791,13792,13793,13794,13795,13796,13798,13799,13800,13801,13802,13803,13806,13807,13881,13882,13904,13979,13980,13983,13984,13985,13987,13988,13991,13993,13994,13995,14060,14061,14222,14380,14381,14382,14383,14418,14419,14420,14421,14666,14667,14668,14858,14859,14860,14861,14862,14863,14868,14869,14870,14872,14873,14874,14875,14876,14877,14878,14879,14881,14882,14885,14886,14887,14888,14889,14890,14897,14898,14899,14900,14901,14903,14904,14906,14907,14908,14909,14911,14912,14913,14914,14915,14917,14918,14919,14920,14921,14924,14925,14926,14927,14928,14930,14931,14933,14934,14935,14936,14937,14938,14939,14940,14942,14943,14944,14946,14947,14948,14949,14950,14951,15050,15051,15052,15053,15055,15099,15100,15102,15122,15124,15125,15239,15240,15242,15608,15609,15610,15611,15612,15613,15614,15615,15616,15617,15685,15686,15687,15688,15689,15691,15692,15693,15694,15695,15696,15698,15702,15703,15704,15705,15706,15707,15708,15709,15712,15713,15718,15725,15726,15727,15730,15731,15732,15733,15734,15737,15739,15746,15754,15758,15777,15781,15786,15797,15798,15799,15800,15801,15803,15804,15807,15808,15809,15837,15838,15839,15840,15841,15842,15843,15844,15845,15847,15850,15852,15854,15855,15858,15859,15860,15861,15862,15863,15864,15866,15867,15868,15869,15870,15871,15873,15876,15882,15883,15885,15886,15887,15888,15889,16756,16757,16758,16759,16760,16761,16763,16764,16765,16766,16767,16768,16769,16770,16771,16777,16778,16779,16780,16781,16782,16783,16784,16785,16786,16787,16788,16789,16790,16791,16792,16793,16794,16795,16796,16797,16798,16799,16800,16801,16802,16803,16804,16806,16807,16808,16809,16810,16811,16812,16813,16814,16815,16818,16819,16820,16821,16822,16823,16826,16827,16833,16834,16835,16836,16838,16839,16840,16841,16842,16843,16844,16845,16846,16847,16848,16850,16851,16852,16853,16854,16855,16856,16857,16858,16860,16861,16862,16863,16864,16865,16866,16867,16868,16869,16876,16877,16886,16887,16888,16889,16891,16893,16894,16895,16900,16901,16902,16903,16907,16908,16909,16912,16917,16918,16919,16920,16921,16922,16928,16932,16934,16935,16936,16944,16964,17037,17060,17061,17062,17064,17065,17067,17123,17126,17129,17152,17227,17230,17234,17235,17237,17238,17240,17243,17244,17245,17247,17248,17253,17258,17260,17264,17265,17266,17267,17308,17309,17368,17395,17396,17398,17399,17404,17405,17406,17408,17410,17411,17412,17413,17414,17415,17470,17471,17474,17478,17479,17483,17510,17511,18522,18524,18525,18526,18527,18577,18578,21077,21078,21079,21080,21081,21083",//自动上架商品ID
        'coupon_type' => 82,
        'limit_ips'   => ['**************', '*************', '************'],
    ],
    //企业微信配置--经销商门户配置
    'wx_qy_setting'    => [
        'corpid'      => 'wx4ca75626b425b0ba',
        'corpsecret'  => 'KRFfNzRzsVA5Lnq4hbblDBFpRyST42HroZAqGWUmIeM',
        'cache_time'  => '6000',
        'back_ly_url' => 'http://dfldata.dongfeng-nissan.com.cn/laravel-nissan/public/index.php/wechat_work/callback',//返回去给研发那边的url
        //        'back_ly_url'=>'http://dfldata-test.dongfeng-nissan.com.cn/laravel-nissan/public/index.php/wechat_work/callback',//返回去给研发那边的url --开发


        'token'            => '7acMbWyRIJt4',
        'encoding_aes_key' => '68uyqv4f4jhPu6DzyAPHMmLvCoqvzqJCyapKnkfmSsm',
    ],

    //启辰企业微信配置
    'qc_wx_qy_setting' => [
        'corpid'     => Env::get('qc_corpid', 'wwd45d569f328f5e37'),
        'corpsecret' => Env::get('qc_corpsecret', 'fcSbwGWSg21ynLmA8Wv7cm_FoUevyl542wgYgOXZKkY'),
        'agentid'    => Env::get('qc_agentid', '1000015'),
    ],

    'wxmp_url'    => 'http://wxmp.chebaba.com/',//weixin项目url
    'sy_url'      => 'https://digitalapi.dongfeng-nissan.com.cn/',
    'e4s_api_url' => 'http://service.chebaba.com',
    'e4s_token'   => '3d2ca3e70a2409718ecce5abcf58430347f4ba2ab2d5e04ecb96e33de51683fcc4757862b46a683979801df2f22b5c985ce2766b5f86720cc7ba2e95da3b861b',
    //传递给南方集团店
    'dlr_auth'    => [
        'url'      => "https://crmdfsouth.dongfeng-click.cn/api/changeUser",
        // 'url'    =>"http://dfldata-test.dongfeng-nissan.com.cn/api/changeUser",
        'dlr_list' => []
    ],

    'chebaba_weixin' => [
        'url'       => Env::get('chebaba_weixin_url', 'https://dfldata-dev.dongfeng-nissan.com.cn/v2/api'),
        'appid'     => Env::get('chebaba_weixin_appid', 'admin'),
        'appsecret' => Env::get('chebaba_weixin_appsecret', 'abc123!!!@'),
    ],


    'jk_goods'                 => [2780, 2782, 2784, 2786, 2788, 2790],
    'pk_goods'                 => [2779, 2781, 2783, 2785, 2787, 2789],
    'jk_goods_16'              => [3310, 3312],
    'pk_goods_16'              => [3309, 3311],
    'lack_product_template_id' => 'qGuvAx08FYbiF2qfkfSMUPDyHbmGCXg-l9OXnlvS58k',
    'jk_one_point_good'        => 3738,

    #转盘活动对应card_id
    'wheel_level_card'         => [1 => 1023, 2 => 1032, 3 => 1033, 4 => 1034, 5 => 0, 10 => 1022],

    #商城1积分兑换商品卡券
    'jk_one_point_card'        => [1038],

    #商城老友汇卡券
    'old_friend_card'          => [1027, 1028],
    'jd_order_key'             => "hvAx08FYbiF2qfkfSMUPDyHbmGCX23hjs",

    #NI慧赚发送积分接口
    'hk_api_url'               => 'https://nissan-api.expservice.com.cn/alliance-abut/api/reward/recordReward',
    'hk_arrival'               => 'https://nissan-api.expservice.com.cn/alliance-abut/api/order/point/receipt',//待确认
    'hotel'                    => [
        'key'    => 'abcdefghijklmnopqrstuvwxyz1234567890',
        'url'    => 'http://obt.dfshanglv.com',
        'create' => 'http://tmc.dfshanglv.com/tmcs/employee/create.json',
        'login'  => 'http://h5.dfshanglv.com/page/sme/login.html'
    ],

    //    'jidou_url' => "https://nissan-test.aijidou.com",//极豆域名
    'jidou_url'                => "https://live.nissan.jidouauto.com",//极豆域名 灰度环境
    'wisdom_url'               => 'https://nvitapp.venucia.com/iov_gw/gw',//智慧加油获取uid 车联

    'open_chebaba'  => [
        'url'        => 'https://open.chebaba.com/',
        'app_id'     => 'cbbECNBMDWCnyEXM76yOVA7V44oxjD5KMdL',
        'app_secret' => 'OWQ3MGI1ODNjY2MyZDNlMzZiYTA2Y2Y0ODg0MjI1ODg='
    ],
    'lianyou_array' => [
        'url' => 'http://cip-if-reverse.chebaba.com/Handler.ashx',//生产
    ],

    'queue_type'     => [
        'default'            => 'default',
        'sms'                => Env::get('queue_sms', 'sms'),
        'member'             => Env::get('queue_member', 'member'),
        'growth'             => Env::get('queue_growth', 'growth'),
        'after_sale'         => Env::get('queue_after_sale', 'after_sale'),
        'order'              => Env::get('queue_order', 'order'),
        'settlement'         => Env::get('queue_settlement', 'settlement'),
        'export'             => Env::get('queue_export', 'export'),
        'hui_ke'             => Env::get('queue_hui_ke', 'hui_ke'),
        'e3s_kafka'          => Env::get('queue_e3s', 'e3s'),
        'order_change'       => Env::get('queue_order_change', 'settlement'),
        'mailer'             => Env::get('queue_mailer', 'mailer'),
        'e3s_dlr'            => Env::get('queue_e3s_dlr', 'e3s_dlr'),
        'coupon'             => Env::get('queue_coupon', 'coupon'),
        'much_card'          => Env::get('queue_much_card', 'much_card'),
        'notifier_card'      => Env::get('queue_notifier_card', 'notifier_card'),
        'pay_order'          => Env::get('queue_pay_order', 'pay_order'),
        'push_order'         => Env::get('queue_push_order', 'push_order'),
        'order_settle_after' => Env::get('queue_order_settle_after', 'order_settle_after'),
        'new_media'          => Env::get('queue_new_media', 'new_media'),

    ],

    //N延保活动卡券
    'insurance_card' => [
        'glod'   => 1482,
        'silver' => 1483,
        'normal' => 1484
    ],

    // 活动
    'activity'       => [
        'vehicle_age' => 'https://e3s-egt.dongfeng-nissan.com.cn',  // 获取车辆年龄
        // 车辆类型
        'cars_list'   => [
            ['goods_name' => '08-12款天籁2.5L/13-18款天籁/西玛/楼兰', 'city_type' => 'A', 'goods_id' => '4282'],
            ['goods_name' => '08-12款天籁2.5L/13-18款天籁/西玛/楼兰', 'city_type' => 'B', 'goods_id' => '4283'],
            ['goods_name' => '08-12款天籁2.5L/13-18款天籁/西玛/楼兰', 'city_type' => 'C', 'goods_id' => '4284'],
            ['goods_name' => '08-12款天籁2.5L/13-18款天籁/西玛/楼兰', 'city_type' => 'D', 'goods_id' => '4285'],
            ['goods_name' => '第七代天籁2.0L/04-12款天籁2.0L', 'city_type' => 'A', 'goods_id' => '4278'],
            ['goods_name' => '第七代天籁2.0L/04-12款天籁2.0L', 'city_type' => 'B', 'goods_id' => '4279'],
            ['goods_name' => '第七代天籁2.0L/04-12款天籁2.0L', 'city_type' => 'C', 'goods_id' => '4280'],
            ['goods_name' => '第七代天籁2.0L/04-12款天籁2.0L', 'city_type' => 'D', 'goods_id' => '4281'],
            ['goods_name' => '第七代天籁2.0T', 'city_type' => 'A', 'goods_id' => '4274'],
            ['goods_name' => '第七代天籁2.0T', 'city_type' => 'B', 'goods_id' => '4275'],
            ['goods_name' => '第七代天籁2.0T', 'city_type' => 'C', 'goods_id' => '4276'],
            ['goods_name' => '第七代天籁2.0T', 'city_type' => 'D', 'goods_id' => '4277'],
            ['goods_name' => '劲客/骐达1.6T/奇骏2.5L/08-12款奇骏2.0L', 'city_type' => 'A', 'goods_id' => '4270'],
            ['goods_name' => '劲客/骐达1.6T/奇骏2.5L/08-12款奇骏2.0L', 'city_type' => 'B', 'goods_id' => '4271'],
            ['goods_name' => '劲客/骐达1.6T/奇骏2.5L/08-12款奇骏2.0L', 'city_type' => 'C', 'goods_id' => '4272'],
            ['goods_name' => '劲客/骐达1.6T/奇骏2.5L/08-12款奇骏2.0L', 'city_type' => 'D', 'goods_id' => '4273'],
            ['goods_name' => '楼兰混动版', 'city_type' => 'A', 'goods_id' => '4266'],
            ['goods_name' => '楼兰混动版', 'city_type' => 'B', 'goods_id' => '4267'],
            ['goods_name' => '楼兰混动版', 'city_type' => 'C', 'goods_id' => '4268'],
            ['goods_name' => '楼兰混动版', 'city_type' => 'D', 'goods_id' => '4269'],
            ['goods_name' => '逍客1.2T', 'city_type' => 'A', 'goods_id' => '4262'],
            ['goods_name' => '逍客1.2T', 'city_type' => 'B', 'goods_id' => '4263'],
            ['goods_name' => '逍客1.2T', 'city_type' => 'C', 'goods_id' => '4264'],
            ['goods_name' => '逍客1.2T', 'city_type' => 'D', 'goods_id' => '4265'],
            ['goods_name' => '轩逸/逍客/12-20款奇骏2.0L/蓝鸟/阳光/新生代骐达/骐达/颐达/骊威/骏逸/玛驰', 'city_type' => 'A', 'goods_id' => '4258'],
            ['goods_name' => '轩逸/逍客/12-20款奇骏2.0L/蓝鸟/阳光/新生代骐达/骐达/颐达/骊威/骏逸/玛驰', 'city_type' => 'B', 'goods_id' => '4259'],
            ['goods_name' => '轩逸/逍客/12-20款奇骏2.0L/蓝鸟/阳光/新生代骐达/骐达/颐达/骊威/骏逸/玛驰', 'city_type' => 'C', 'goods_id' => '4260'],
            ['goods_name' => '轩逸/逍客/12-20款奇骏2.0L/蓝鸟/阳光/新生代骐达/骐达/颐达/骊威/骏逸/玛驰', 'city_type' => 'D', 'goods_id' => '4261'],
        ],
        // sku
        'splist'      => [
            'litre'  => [
                893 => ['val' => '4L'],
                894 => ['val' => '5L'],
                895 => ['val' => '6L'],
            ],
            'number' => [
                2900 => ['val' => '2次', "discount" => 7],
                2901 => ['val' => '2次', "discount" => 7.5],
            ],
            'oil'    => [
                899 => ['val' => '普通油(5W-30)'],
                900 => ['val' => '合成油（5W-30）'],
                901 => ['val' => '全合成油（5W-40）'],
                902 => ['val' => '全合成油（5W-30）'],
                903 => ['val' => '超级全合成油（0W-20）'],
                904 => ['val' => '超级全合成油1（0W-40）'],
                905 => ['val' => '超级全合成油2（0W-40）'],
            ],

        ],
    ],

    'draw22618' => [
        'act_code'   => 'draw22618',
        'start_date' => '2022-06-01 00:00:00',
        'end_date'   => '2022-06-22 23:59:59',
        'draw_id'    => [
            1  => 3826,
            2  => 3609,
            3  => 5082,
            4  => 5084,
            5  => 5081,
            6  => 0,
            7  => 0,
            8  => 0,
            9  => 1307,
            10 => 1307,
        ],
    ],

    'draw_order_act' => [
        'act_code'       => 'draw2210_12', // 订单抽奖
        'redis_pre_name' => 'draw2210_12_prizes',
        'start_date'     => '2022-10-01 00:00:00',
        'end_date'       => '2022-12-31 23:59:59',
        'card_id'        => [
            1 => 0,
            2 => 0,
            3 => 1439,
            4 => 1438,
            5 => 1437,
            6 => 1436,
            7 => 1435,
            8 => 1434,
        ],
    ],

    'new_user_act' => [
        'new_act_code' => 'new20221012', // 新人活动
        'start_date'   => '2022-10-01 00:00:00',
        'end_date'     => '2022-10-31 23:59:59',
        'NEW_CARD'     => [
            1440, 1441, 1442
        ],
        'URL_GWAPP'    => 'oneapp/Special?id=55',
        'URL_GWSM'     => 'package_mall/pages/mall_index/special/index?id=8',
    ],

    'draw_220801' => [
        'act_code'       => 'draw220801', // 启辰抽奖
        'redis_pre_name' => 'draw220801_prizes',
        'start_date'     => '2022-08-01 00:00:00',
        'end_date'       => '2022-08-31 23:59:59',
        'draw_id'        => [
            1 => 1390,
            2 => 1389,
            3 => 1391,
            4 => 1385,
            5 => 1384,
            6 => 1387,
            7 => 1388,
            8 => 1386,
        ],
    ],

    //五年双保和心悦1
    'friend'      => [
        'vehicle_age'           => "https://e3s-egt.dongfeng-nissan.com.cn", // 获取车辆年龄
        'double_commodity_list' => [4256, 4257],
        'double_spec_list'      => [182, 183, 184],
        'heart_commodity_id'    => [4258, 4259, 4260, 4261, 4262, 4263, 4264, 4265, 4266, 4267, 4268, 4269, 4270, 4271, 4272, 4273, 4274, 4275, 4276, 4277, 4278, 4279, 4280, 4281, 4282, 4283, 4284, 4285],
        'heart_sp_index_id'     => 185,//规格
        'heart_sp_value_id'     => [
            1 => 2900,//7折
            2 => 2901,//7.5折
            3 => 3174,//8折
        ],
    ],
    # 老优惠、五年双保、心悦
    'maintenance' => [ // 生产环境
                       'lyh_act_type_id'   => 8, // 活动类型ID：1限时折扣；2团购；3满减；4全积分折扣；5套装；6N件N折；7预售；8立减
                       'lyh_act_id'        => date('Y-m-d H:i:s') >= '2022-06-01 00:00:00' ? 501 : 481, // 活动类型对应ID
                       'lyh_start_time'    => '2022-05-01 00:00:00', // 老友惠活动开始时间
                       'lyh_end_time'      => '2022-06-30 23:59:59', // 老友惠活动结束时间
                       'lyh_un_start_time' => '2022-05-01 00:00:00', // 老友惠删除普通油开始时间
                       'lyh_un_end_time'   => '2022-06-30 23:59:59', // 老友惠删除普通油结束时间

                       'xy_act_type_id'   => 8, // 活动类型ID：1限时折扣；2团购；3满减；4全积分折扣；5套装；6N件N折；7预售；8立减
                       'xy_act_id'        => 501, // 活动类型对应ID
                       'xy_start_time'    => '2022-06-01 00:00:00', // 心悦活动开始时间
                       'xy_end_time'      => '2022-06-30 23:59:59', // 心悦活动结束时间
                       'xy_un_start_time' => '2021-06-01 00:00:00', // 心悦删除普通油开始时间
                       'xy_un_end_time'   => '2022-06-30 23:59:59', // 心悦删除普通油结束时间

                       'sb_act_type_id' => 8, // 活动类型ID：1限时折扣；2团购；3满减；4全积分折扣；5套装；6N件N折；7预售；8立减
                       'sb_act_id'      => 573, // 活动类型对应ID
                       'sb_start_time'  => date('Y-m-d H:i:s') >= '2022-08-16 00:00:00' ? '2022-08-16 00:00:00' : (date('Y-m-d H:i:s') >= '2022-07-16 00:00:00' ? '2022-07-16 00:00:00' : '2022-06-30 00:00:00'), // 双保活动开始时间
                       'sb_end_time'    => date('Y-m-d H:i:s') >= '2022-08-16 00:00:00' ? '2022-08-22 23:59:59' : (date('Y-m-d H:i:s') >= '2022-07-16 00:00:00' ? '2022-07-22 23:59:59' : '2022-07-07 23:59:59'), // 双保活动结束时间
    ],

    'combination_judge_ids'  => [
        'upgrade_id' => 4784, // 五年双保机油升级套餐  ID
        'music_id'   => 4781, // 音乐一年包 ID
        'fresh_id'   => 4790, // 空调清新套餐 ID
        'basic_id'   => 4783, // 单次基础保养套餐 ID
    ],

    // e3s接口
    'DNDC_GET_CAR_AGE'       => 'https://e3s-egt.dongfeng-nissan.com.cn/postdata/DNDC_ONLINESHOP/DNDC_GET_CAR_AGE',
    'DNDC_GET_MAINTAIN_INFO' => 'https://e3s-egt.dongfeng-nissan.com.cn/postdata/DNDC_ONLINESHOP//DNDC_GET_MAINTAIN_INFO',

    // 短链版本
    'url_version'            => 'release',

    'fu_pin' => [
//        'fruits' => [5303],
//        'agr'    => [4578,4562,4561,4500,4499,4498,4497,4879,4878,5753,5660,5659,7314,7310]
'fruits' => [5850],
'agr'    => [5657, 4879, 4669, 4670, 4874, 4873, 4870, 4868, 5753, 5660]
    ],

    'not_mid'        => ['60251512568466636800'],

    // 积分加密配置
    'point'          => [
        'appId'       => Env::get('POINT_APP_ID', 'P_SR_002'),
        'app_secret'  => Env::get('POINT_APP_SECRET', '1UXXMZ4raMIQjXWf9Z2ReVjdRwGFfeW7'),
        'private_key' => Env::get('POINT_PRIVATE_KEY', 'MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCEl5hJ0W8BpX5mDPJyeHcm2bftlt/If/JWpw5Nwj1e6IRje69/CHbS+U9jJ13KwQyl14GOSfGdCX6FcoEsvbQnE/A1CquQdpnwitsvVmoOAELiud5QwbyGBp3mPMBP3WhuHCi5G3mXNtv0wfd9oDqiKp1VxA/2lSs/Ya5L9ji6bfWOgCiKTS5XdPz+3K/4ORS0c5dcZ3je2IH6Oi85UBGn8MiSd/97yRNLchKmHBzHDaOuREXR0QxrjkESc/PysTQazB3yDJIPPHsy7oa0FZBAe06I9+OLegKxPZ7ek8ekJhZhaFAgHc7N8A7g8/Nyr/NfIwbGtl+y6g7CI7u/KRqRAgMBAAECggEATFBUPmhkU7ZEwcfB41Cig6Hb0st7kZLSkD0mZmrdl7qMOBYANEVS3qrPnmoR+ZiTBwC6eILmpVPJllUJNA8bmUJIhr7VcBhTEbdZ4C0EbXqJBKWl5yUatKkqSFG+/jiY2ocqiR/fI8FXZ9Ab+ho2pQ+R7sMhzPKeKaS3BNF6ZRIAhbQKnr6md5f/pAbgkzm2QzOdrxNZdqaKIVq5dCSENIICARyWNtZuL8KEqhGC9/RGlYPaEaUxIxZnk8OZ0ekepAIAOeBdZjwGVBU/n9iju52+DGvvUC7xL+0b8QJ+bjI0e9PMsdpyaxr80sT/T+fENaa6IEdnCqgiTjW07oDd4QKBgQC+xIPn/3FIcce/BmcGZrKSS/90DSuP2e+ytSKlwsb7zr699FrberlpqSsgqZxfik5kA1qgA8B8tC2VxTY+juHKRxhQIzx6sdMUccF282dJ1c8/x/GYO719g4VTXgqY4HaComx4+Jez/81suyNuSG1jSGi5uUlta38uqCAoUWan8wKBgQCx7n6qgch6uyyfdOnScCxC52+76feajLNg1cEwbV+aDJ/xuryb6wpOwbWDhQIs6WjgJqJOn91llOdNW2Py0G8lG/biMkbkqb2U54nFmw173kF3M9g6EnMM0I/DxyE2QJ0nK3Z9JkfofZRTwuCF5FWljYE9GLVhWief77feHB14awKBgFwYlTGwYWk+/Nac6anudHEqXWYSYaBt70lQJyztLpEsS2t7B04EHnUDJX38RDDfnV6tO9gaV5gciBFRBxIGerzju7oceLsnFCc/j820Tn4XyZkaFumOlbl9gCUyNl19p9kDPujb4qkUaoD+i2lXb+/Jaj2XVPqNNfY5VmUAf1W/AoGBAJrJUr+Zr4G1GpiM6Gv80URSeU0LuC5mgBWZXjp2q1gj63AqdrFcwtbIhNMo/j97vZRhh4jGYZ9g7ebJviUZVdb+gMM94ZZCY34J/jw9Knm7W1ALderu6c8YEDjMHgRIKW5NzQIKLzv9Iu/K8+8GN8uRUgCIYiGSgC4OJ/G8m5aJAoGAQzdIPr4h/y8oQDHm52rmfEhMiNAOiyULedZIEuBEiqveAFVPmXhw6CTTWt71NW85X1QvmcmMWBUq9NIy4+Nino0GHqgn2Fakawp9OLUKXNUZUqqW2ecSfNabpZSsOmWp7/3Xa0bU4VXcRkszIivdNjvzdvnK0SoNaIqTAVI6mI8='),
        'public_key'  => Env::get('POINT_PUBLIC_KEY', 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAhJeYSdFvAaV+Zgzycnh3Jtm37ZbfyH/yVqcOTcI9XuiEY3uvfwh20vlPYyddysEMpdeBjknxnQl+hXKBLL20JxPwNQqrkHaZ8IrbL1ZqDgBC4rneUMG8hgad5jzAT91obhwouRt5lzbb9MH3faA6oiqdVcQP9pUrP2GuS/Y4um31joAoik0uV3T8/tyv+DkUtHOXXGd43tiB+jovOVARp/DIknf/e8kTS3ISphwcxw2jrkRF0dEMa45BEnPz8rE0Gswd8gySDzx7Mu6GtBWQQHtOiPfji3oCsT2e3pPHpCYWYWhQIB3OzfAO4PPzcq/zXyMGxrZfsuoOwiO7vykakQIDAQAB')
    ],

    // 大转盘
    'big_wheel_draw' => [
        'act_code'       => 'draw_221111', // 订单抽奖
        'redis_pre_name' => 'draw_221111_prizes',
        'start_date'     => '2022-11-01 00:00:00',
        'end_date'       => '2022-11-30 23:59:59',
        'draw_list'      => [
            1 => ['draw_type' => 4, 'draw_level' => 1, 'draw_name' => '1000积分', 'draw_id' => 0, 'draw_quota' => 1000, 'pro' => 100, 'number' => 200, 'img_url' => Env::get('env_img_domain_url', 'https://wxstoreimg.dongfeng-nissan.com.cn/') . 'public/static/ac_special/big_wheel/7aa41d1ca161d5be.png', 'img_url_l' => Env::get('env_img_domain_url', 'https://wxstoreimg.dongfeng-nissan.com.cn/') . 'public/static/ac_special/big_wheel/7aa41d1ca161d5be_l.png'],
            2 => ['draw_type' => 4, 'draw_level' => 2, 'draw_name' => '500积分', 'draw_id' => 0, 'draw_quota' => 500, 'pro' => 200, 'number' => 200, 'img_url' => Env::get('env_img_domain_url', 'https://wxstoreimg.dongfeng-nissan.com.cn/') . 'public/static/ac_special/big_wheel/aefa92d0c31d3ff6.png', 'img_url_l' => Env::get('env_img_domain_url', 'https://wxstoreimg.dongfeng-nissan.com.cn/') . 'public/static/ac_special/big_wheel/aefa92d0c31d3ff6_l.png'],
            3 => ['draw_type' => 4, 'draw_level' => 3, 'draw_name' => '128积分', 'draw_id' => 0, 'draw_quota' => 128, 'pro' => 300, 'number' => 600, 'img_url' => Env::get('env_img_domain_url', 'https://wxstoreimg.dongfeng-nissan.com.cn/') . 'public/static/ac_special/big_wheel/5616c33523f197e9.png', 'img_url_l' => Env::get('env_img_domain_url', 'https://wxstoreimg.dongfeng-nissan.com.cn/') . 'public/static/ac_special/big_wheel/5616c33523f197e9_l.png'],
            4 => ['draw_type' => 4, 'draw_level' => 4, 'draw_name' => '66积分', 'draw_id' => 0, 'draw_quota' => 66, 'pro' => 1000, 'number' => 4000, 'img_url' => Env::get('env_img_domain_url', 'https://wxstoreimg.dongfeng-nissan.com.cn/') . 'public/static/ac_special/big_wheel/e515dbf19b090bd9.png', 'img_url_l' => Env::get('env_img_domain_url', 'https://wxstoreimg.dongfeng-nissan.com.cn/') . 'public/static/ac_special/big_wheel/e515dbf19b090bd9_l.png'],
            5 => ['draw_type' => 4, 'draw_level' => 5, 'draw_name' => '18积分', 'draw_id' => 0, 'draw_quota' => 18, 'pro' => 6600, 'number' => 20000, 'img_url' => Env::get('env_img_domain_url', 'https://wxstoreimg.dongfeng-nissan.com.cn/') . 'public/static/ac_special/big_wheel/a2e8c6b452ae2677.png', 'img_url_l' => Env::get('env_img_domain_url', 'https://wxstoreimg.dongfeng-nissan.com.cn/') . 'public/static/ac_special/big_wheel/a2e8c6b452ae2677_l.png'],
            6 => ['draw_type' => 2, 'draw_level' => 6, 'draw_name' => '10元备件电商优惠券', 'draw_id' => 1464, 'draw_quota' => 10, 'pro' => 1800, 'number' => 9999999, 'img_url' => Env::get('env_img_domain_url', 'https://wxstoreimg.dongfeng-nissan.com.cn/') . 'public/static/ac_special/big_wheel/49b75c9d62ae6eb6.png', 'img_url_l' => Env::get('env_img_domain_url', 'https://wxstoreimg.dongfeng-nissan.com.cn/') . 'public/static/ac_special/big_wheel/49b75c9d62ae6eb6_l.png'],
        ],
        'share_img'      => Env::get('env_img_domain_url', 'https://wxstoreimg.dongfeng-nissan.com.cn/') . 'public/static/ac_special/big_wheel/share.jpg',
        'share_title'    => '双11养车限时抢，惊喜盲盒免费抽',
        'share_url'      => 'package_mall/pages/mall_index/special/index?id=309',
    ],

    'qc_friend_litre' => [5369, 5370, 5371],
    'qc_yue_litre'    => [5375, 5376, 5377],

    'tag_code'     => [
        'small_index_code'    => '2d7820ab19',
        'app_index_code'      => '9b16d27ca2',
        'qc_small_index_code' => 'e37a7c6daa',
        'qc_app_index_code'   => 'b00629ea67',
    ],

    // cap活动
    'activity_cap' => [
        'act_code'             => 'activity_61',
        'act_id'               => '71033',
        'start_date'           => '2023-05-30 00:00:00',
        'end_date'             => '2023-06-11 23:59:59',
        'commodity_ids'        => [
            6555, 6556, 6557, 6558
        ],
        'key'                  => 'Oa3jNqWeA5x7f2WM1BhrYTdEUs5titgvk3RRbFcI',
        'url'                  => 'https://digital-official-api.dongfeng-nissan.com.cn/',
        'user_prize_check_url' => 'platform/api/activity/cap-61/user-prize-check',
        'user_prize_notify'    => 'platform/api/activity/cap-61/user-prize-notify',
    ],
    'sso_oauth'    => [
        "oauth_url"       => Env::get('OAUTH_URL'),
        "oauth_url_token" => Env::get('OAUTH_URL_TOKEN'),
        "client_id"       => Env::get("OAUTH_CLIENT_ID"),
        "client_secret"   => Env::get("OAUTH_CLIENT_SECRET"),
        "app_id"          => Env::get("OAUTH_APP_ID"),
        "app_secret"      => Env::get("OAUTH_APP_SECRET"),
        'redirect_url'    => Env::get('OAUTH_REDIRECT_URL'),
    ],

    // cap活动商品id
    'cap_activity' => [
        // 到店活动
        [
            'data' => [
                ['commodity_id' => '10297', 'card_id' => '*****************', 'commodity_name' => 'HUAWEI MatePad 11.5', 'quick_win_card_id' => '1423855'],
                ['commodity_id' => '10299', 'card_id' => '*****************', 'commodity_name' => '九阳（Joyoung） 空气炸锅', 'quick_win_card_id' => '1423856'],
                ['commodity_id' => '10300', 'card_id' => '*****************', 'commodity_name' => '火枫（FIRE-MAPLE）京宴野营套锅', 'quick_win_card_id' => '1423857'],
                ['commodity_id' => '10301', 'card_id' => '*****************', 'commodity_name' => '齐心(Comix)手持小风扇', 'quick_win_card_id' => '1423858'],
                ['commodity_id' => '10302', 'card_id' => '*****************', 'commodity_name' => 'N7便携反光板折叠团扇', 'quick_win_card_id' => '1423859'],
            ],
            'path' => 'api/wechat/check-user-orders'
        ],
        // 车展活动
        [
            'data' => [
                ['commodity_id' => '10038', 'card_id' => '*****************', 'commodity_name' => '奈雪的茶奶茶券', 'quick_win_card_id' => '1423239'],
                ['commodity_id' => '10037', 'card_id' => '*****************', 'commodity_name' => '霸王别姬奶茶券', 'quick_win_card_id' => '1423238'],
                ['commodity_id' => '10036', 'card_id' => '*****************', 'commodity_name' => '古茗奶茶券', 'quick_win_card_id' => '1423237'],
                ['commodity_id' => '10035', 'card_id' => '*****************', 'commodity_name' => '蜜雪冰城奶茶券', 'quick_win_card_id' => '1423236'],
                ['commodity_id' => '10144', 'card_id' => '*****************', 'commodity_name' => '蜜雪冰城奶茶券', 'quick_win_card_id' => '1423444'],
            ],
            'path' => 'api/wechat/check-user-order-city',
        ],
    ],

    'dy_qrcode_url' => Env::get('DY_QRCODE_URL', 'https://feat-qy-sit-dndcfenge-node-activity.nissan-website.dev.dndc.cloud/'),

];
